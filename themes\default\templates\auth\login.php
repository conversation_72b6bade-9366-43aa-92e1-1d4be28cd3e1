<?php
// Set layout variables
$body_class = 'auth-page';
$container_class = 'container';

// Start output buffering for content
ob_start();
?>

<div class="row justify-content-center min-vh-100 align-items-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow-lg border-0">
            <div class="card-body p-5">
                <!-- Logo and title -->
                <div class="text-center mb-4">
                    <div class="mb-3">
                        <i class="fas fa-project-diagram text-primary" style="font-size: 3rem;"></i>
                    </div>
                    <h1 class="h3 mb-1 fw-bold">Welcome Back</h1>
                    <p class="text-muted">Sign in to your ProjectManager account</p>
                </div>
                
                <!-- Login form -->
                <form method="post" action="<?php echo $this->url('login'); ?>" novalidate>
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <?php if (!empty($redirect)): ?>
                        <input type="hidden" name="redirect" value="<?php echo $this->escapeAttr($redirect); ?>">
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-envelope"></i>
                            </span>
                            <input type="email" 
                                   class="form-control" 
                                   id="email" 
                                   name="email" 
                                   placeholder="Enter your email"
                                   required 
                                   autofocus>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" 
                                   class="form-control" 
                                   id="password" 
                                   name="password" 
                                   placeholder="Enter your password"
                                   required>
                            <button class="btn btn-outline-secondary" 
                                    type="button" 
                                    id="togglePassword"
                                    title="Show/Hide Password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember" name="remember">
                        <label class="form-check-label" for="remember">
                            Remember me
                        </label>
                    </div>
                    
                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            Sign In
                        </button>
                    </div>
                </form>
                
                <!-- Additional links -->
                <div class="text-center">
                    <a href="<?php echo $this->url('forgot-password'); ?>" class="text-decoration-none">
                        Forgot your password?
                    </a>
                </div>
                
                <?php if ($this->isRegistrationEnabled()): ?>
                    <hr class="my-4">
                    <div class="text-center">
                        <p class="mb-2">Don't have an account?</p>
                        <a href="<?php echo $this->url('register'); ?>" class="btn btn-outline-primary">
                            <i class="fas fa-user-plus me-2"></i>
                            Create Account
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="text-center mt-4">
            <p class="text-muted small">
                &copy; <?php echo date('Y'); ?> ProjectManager. All rights reserved.
            </p>
        </div>
    </div>
</div>

<style>
.auth-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.auth-content {
    padding: 0;
}

.card {
    border-radius: 15px;
}

.input-group-text {
    background-color: #f8f9fa;
    border-right: none;
}

.form-control {
    border-left: none;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    
    if (togglePassword && passwordInput) {
        togglePassword.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            
            const icon = this.querySelector('i');
            icon.classList.toggle('fa-eye');
            icon.classList.toggle('fa-eye-slash');
        });
    }
    
    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                e.preventDefault();
                alert('Please fill in all required fields.');
                return false;
            }
            
            if (!isValidEmail(email)) {
                e.preventDefault();
                alert('Please enter a valid email address.');
                return false;
            }
        });
    }
    
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
});
</script>

<?php
$content = ob_get_clean();

// Include the layout
include PM_THEMES_PATH . '/default/templates/layout.php';
?>
