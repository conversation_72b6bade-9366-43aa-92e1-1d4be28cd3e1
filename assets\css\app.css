/* ProjectManager Custom Styles */

/* Variables */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --sidebar-width: 250px;
    --navbar-height: 60px;
}

/* Global Styles */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f8f9fa;
}

/* Layout */
.main-content {
    margin-top: var(--navbar-height);
    min-height: calc(100vh - var(--navbar-height));
}

.auth-content {
    min-height: 100vh;
}

/* Navigation */
.navbar {
    height: var(--navbar-height);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    transform: translateY(-1px);
}

.nav-link.active {
    background-color: rgba(255,255,255,0.1);
    border-radius: 0.375rem;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}

/* Buttons */
.btn {
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Forms */
.form-control {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* Progress bars */
.progress {
    border-radius: 0.5rem;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 0.5rem;
}

/* Badges */
.badge {
    font-weight: 500;
    border-radius: 0.375rem;
}

/* List groups */
.list-group-item {
    border: none;
    border-bottom: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

.list-group-item:last-child {
    border-bottom: none;
}

/* Tables */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

/* Dropdowns */
.dropdown-menu {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: 0.5rem;
}

.dropdown-item {
    font-weight: 500;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

/* Avatar */
.avatar-placeholder {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    font-weight: 600;
}

/* Status colors */
.status-active { color: var(--success-color); }
.status-inactive { color: var(--danger-color); }
.status-pending { color: var(--warning-color); }
.status-completed { color: var(--success-color); }
.status-in-progress { color: var(--info-color); }
.status-todo { color: var(--warning-color); }
.status-review { color: var(--info-color); }
.status-done { color: var(--success-color); }

/* Priority colors */
.priority-low { color: #6c757d; }
.priority-medium { color: var(--info-color); }
.priority-high { color: var(--warning-color); }
.priority-urgent { color: var(--danger-color); }

/* Utility classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.shadow-soft {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.shadow-medium {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.border-radius-lg {
    border-radius: 0.75rem;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .main-content {
        margin-top: var(--navbar-height);
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn {
        font-size: 0.875rem;
    }
    
    .table-responsive {
        border-radius: 0.5rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --bs-body-bg: #1a1a1a;
        --bs-body-color: #ffffff;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .form-control {
        background-color: #3d3d3d;
        border-color: #555;
        color: #ffffff;
    }
    
    .form-control:focus {
        background-color: #3d3d3d;
        border-color: var(--primary-color);
        color: #ffffff;
    }
}

/* Print styles */
@media print {
    .navbar,
    .btn,
    .alert,
    .dropdown {
        display: none !important;
    }
    
    .main-content {
        margin-top: 0;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}

/* Loading spinner */
.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
