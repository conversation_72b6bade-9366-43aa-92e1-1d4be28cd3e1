<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $this->escape($page_title ?? 'ProjectManager'); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="<?php echo $this->asset('css/bootstrap.min.css'); ?>" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?php echo $this->asset('css/app.css'); ?>" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo $this->asset('images/favicon.ico'); ?>">
    
    <!-- Meta tags -->
    <meta name="description" content="ProjectManager - Organize and manage your projects efficiently">
    <meta name="author" content="ProjectManager">
    
    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?php echo $csrf_token; ?>">
</head>
<body class="<?php echo $body_class ?? ''; ?>">
    
    <!-- Navigation -->
    <?php if ($is_logged_in): ?>
        <?php $this->include('navigation'); ?>
    <?php endif; ?>
    
    <!-- Main Content -->
    <main class="<?php echo $is_logged_in ? 'main-content' : 'auth-content'; ?>">
        
        <!-- Flash Messages -->
        <?php if (!empty($flash_messages)): ?>
            <div class="container-fluid">
                <?php foreach ($flash_messages as $type => $messages): ?>
                    <?php foreach ($messages as $message): ?>
                        <div class="alert alert-<?php echo $type === 'error' ? 'danger' : $type; ?> alert-dismissible fade show" role="alert">
                            <?php echo $this->escape($message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endforeach; ?>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <!-- Page Content -->
        <div class="<?php echo $container_class ?? 'container-fluid'; ?>">
            <?php echo $content ?? ''; ?>
        </div>
        
    </main>
    
    <!-- Footer -->
    <?php if ($is_logged_in): ?>
        <?php $this->include('footer'); ?>
    <?php endif; ?>
    
    <!-- Bootstrap JS -->
    <script src="<?php echo $this->asset('js/bootstrap.bundle.min.js'); ?>"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo $this->asset('js/app.js'); ?>"></script>
    
    <!-- Page-specific scripts -->
    <?php if (isset($page_scripts)): ?>
        <?php foreach ($page_scripts as $script): ?>
            <script src="<?php echo $this->asset($script); ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Inline scripts -->
    <?php if (isset($inline_scripts)): ?>
        <script>
            <?php echo $inline_scripts; ?>
        </script>
    <?php endif; ?>
    
</body>
</html>
