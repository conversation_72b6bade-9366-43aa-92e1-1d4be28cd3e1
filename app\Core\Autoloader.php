<?php
/**
 * PSR-4 Compliant Autoloader
 * 
 * Custom autoloader for ProjectManager without Composer dependency
 * 
 * @package ProjectManager\Core
 */

declare(strict_types=1);

namespace App\Core;

/**
 * PSR-4 Autoloader Class
 */
class Autoloader
{
    /**
     * Namespace prefixes and their base directories
     * 
     * @var array
     */
    private static array $prefixes = [];
    
    /**
     * Register the autoloader
     */
    public static function register(): void
    {
        spl_autoload_register([self::class, 'loadClass']);
        
        // Register default namespaces
        self::addNamespace('App\\', PM_APP_PATH . '/');
        self::addNamespace('Modules\\', PM_MODULES_PATH . '/');
        self::addNamespace('Themes\\', PM_THEMES_PATH . '/');
        self::addNamespace('Plugins\\', PM_PLUGINS_PATH . '/');
    }
    
    /**
     * Add a namespace prefix and base directory
     * 
     * @param string $prefix The namespace prefix
     * @param string $baseDir The base directory for the namespace
     */
    public static function addNamespace(string $prefix, string $baseDir): void
    {
        // Normalize namespace prefix
        $prefix = trim($prefix, '\\') . '\\';
        
        // Normalize base directory
        $baseDir = rtrim($baseDir, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR;
        
        // Initialize the namespace prefix array if needed
        if (!isset(self::$prefixes[$prefix])) {
            self::$prefixes[$prefix] = [];
        }
        
        // Add the base directory to the namespace prefix
        array_push(self::$prefixes[$prefix], $baseDir);
    }
    
    /**
     * Load a class file
     * 
     * @param string $class The fully qualified class name
     * @return bool True if the file was loaded, false otherwise
     */
    public static function loadClass(string $class): bool
    {
        // Work backwards through the namespace prefixes to find a match
        $prefix = $class;
        while (false !== $pos = strrpos($prefix, '\\')) {
            // Retain the trailing namespace separator in the prefix
            $prefix = substr($class, 0, $pos + 1);
            
            // The rest is the relative class name
            $relativeClass = substr($class, $pos + 1);
            
            // Try to load a mapped file for the prefix and relative class
            $mappedFile = self::loadMappedFile($prefix, $relativeClass);
            if ($mappedFile) {
                return $mappedFile;
            }
            
            // Remove the trailing namespace separator for the next iteration
            $prefix = rtrim($prefix, '\\');
        }
        
        return false;
    }
    
    /**
     * Load the mapped file for a namespace prefix and relative class
     * 
     * @param string $prefix The namespace prefix
     * @param string $relativeClass The relative class name
     * @return bool True if the file was loaded, false otherwise
     */
    private static function loadMappedFile(string $prefix, string $relativeClass): bool
    {
        // Are there any base directories for this namespace prefix?
        if (!isset(self::$prefixes[$prefix])) {
            return false;
        }
        
        // Look through base directories for this namespace prefix
        foreach (self::$prefixes[$prefix] as $baseDir) {
            // Replace namespace separators with directory separators
            // in the relative class name, append with .php
            $file = $baseDir . str_replace('\\', DIRECTORY_SEPARATOR, $relativeClass) . '.php';
            
            // If the mapped file exists, require it
            if (self::requireFile($file)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Require a file if it exists
     * 
     * @param string $file The file to require
     * @return bool True if the file was required, false otherwise
     */
    private static function requireFile(string $file): bool
    {
        if (file_exists($file)) {
            require $file;
            return true;
        }
        
        return false;
    }
    
    /**
     * Get all registered namespaces
     * 
     * @return array
     */
    public static function getNamespaces(): array
    {
        return self::$prefixes;
    }
}

// Register the autoloader
Autoloader::register();
