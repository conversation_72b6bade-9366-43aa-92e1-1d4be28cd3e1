<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <!-- Brand -->
        <a class="navbar-brand fw-bold" href="<?php echo $this->url(); ?>">
            <i class="fas fa-project-diagram me-2"></i>
            ProjectManager
        </a>
        
        <!-- Mobile toggle -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <!-- Navigation items -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link <?php echo $this->getCurrentRoute() === '' ? 'active' : ''; ?>" 
                       href="<?php echo $this->url('dashboard'); ?>">
                        <i class="fas fa-tachometer-alt me-1"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo strpos($this->getCurrentRoute(), 'projects') === 0 ? 'active' : ''; ?>" 
                       href="<?php echo $this->url('projects'); ?>">
                        <i class="fas fa-folder me-1"></i>
                        Projects
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo strpos($this->getCurrentRoute(), 'tasks') === 0 ? 'active' : ''; ?>" 
                       href="<?php echo $this->url('tasks'); ?>">
                        <i class="fas fa-tasks me-1"></i>
                        Tasks
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo strpos($this->getCurrentRoute(), 'calendar') === 0 ? 'active' : ''; ?>" 
                       href="<?php echo $this->url('calendar'); ?>">
                        <i class="fas fa-calendar me-1"></i>
                        Calendar
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo strpos($this->getCurrentRoute(), 'reports') === 0 ? 'active' : ''; ?>" 
                       href="<?php echo $this->url('reports'); ?>">
                        <i class="fas fa-chart-bar me-1"></i>
                        Reports
                    </a>
                </li>
            </ul>
            
            <!-- Right side navigation -->
            <ul class="navbar-nav">
                <!-- Notifications -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle position-relative" href="#" id="notificationsDropdown" 
                       role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                            3
                            <span class="visually-hidden">unread notifications</span>
                        </span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                        <li><h6 class="dropdown-header">Notifications</h6></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="#">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-user-plus text-success"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-2">
                                        <div class="fw-bold">New team member</div>
                                        <div class="small text-muted">John joined Project Alpha</div>
                                        <div class="small text-muted">2 hours ago</div>
                                    </div>
                                </div>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-check-circle text-success"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-2">
                                        <div class="fw-bold">Task completed</div>
                                        <div class="small text-muted">Design mockups finished</div>
                                        <div class="small text-muted">4 hours ago</div>
                                    </div>
                                </div>
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-center" href="<?php echo $this->url('notifications'); ?>">View all notifications</a></li>
                    </ul>
                </li>
                
                <!-- User menu -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" 
                       role="button" data-bs-toggle="dropdown">
                        <div class="avatar me-2">
                            <?php if (!empty($current_user['avatar'])): ?>
                                <img src="<?php echo $this->escape($current_user['avatar']); ?>" 
                                     alt="<?php echo $this->escape($current_user['full_name']); ?>" 
                                     class="rounded-circle" width="32" height="32">
                            <?php else: ?>
                                <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center bg-secondary text-white" 
                                     style="width: 32px; height: 32px; font-size: 14px;">
                                    <?php echo strtoupper(substr($current_user['full_name'], 0, 1)); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <span class="d-none d-md-inline"><?php echo $this->escape($current_user['full_name']); ?></span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><h6 class="dropdown-header"><?php echo $this->escape($current_user['email']); ?></h6></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="<?php echo $this->url('profile'); ?>">
                                <i class="fas fa-user me-2"></i>
                                My Profile
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo $this->url('settings'); ?>">
                                <i class="fas fa-cog me-2"></i>
                                Settings
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <?php if ($this->hasPermission('admin')): ?>
                            <li>
                                <a class="dropdown-item" href="<?php echo $this->url('admin'); ?>">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    Administration
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                        <?php endif; ?>
                        <li>
                            <a class="dropdown-item" href="<?php echo $this->url('logout'); ?>">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Logout
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Breadcrumb -->
<?php if (!empty($breadcrumbs)): ?>
<nav aria-label="breadcrumb" class="bg-light border-bottom">
    <div class="container-fluid">
        <ol class="breadcrumb mb-0 py-2">
            <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                <?php if ($index === count($breadcrumbs) - 1): ?>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?php echo $this->escape($breadcrumb['title']); ?>
                    </li>
                <?php else: ?>
                    <li class="breadcrumb-item">
                        <a href="<?php echo $this->escape($breadcrumb['url']); ?>">
                            <?php echo $this->escape($breadcrumb['title']); ?>
                        </a>
                    </li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ol>
    </div>
</nav>
<?php endif; ?>
