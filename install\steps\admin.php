<?php
$errors = [];
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $adminData = [
        'full_name' => trim($_POST['admin_name'] ?? ''),
        'email' => trim($_POST['admin_email'] ?? ''),
        'password' => $_POST['admin_password'] ?? '',
        'password_confirm' => $_POST['admin_password_confirm'] ?? ''
    ];
    
    // Validate input
    if (empty($adminData['full_name'])) {
        $errors[] = 'Full name is required';
    }
    
    if (empty($adminData['email'])) {
        $errors[] = 'Email address is required';
    } elseif (!filter_var($adminData['email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid email address';
    }
    
    if (empty($adminData['password'])) {
        $errors[] = 'Password is required';
    } elseif (strlen($adminData['password']) < 8) {
        $errors[] = 'Password must be at least 8 characters long';
    }
    
    if ($adminData['password'] !== $adminData['password_confirm']) {
        $errors[] = 'Passwords do not match';
    }
    
    if (empty($errors)) {
        // Get database config from session
        $dbConfig = $_SESSION['db_config'] ?? null;
        
        if (!$dbConfig) {
            $errors[] = 'Database configuration not found. Please go back and configure the database first.';
        } else {
            // Create admin user
            $result = $installer->createAdminUser($dbConfig, $adminData);
            
            if ($result['success']) {
                // Generate configuration file
                $configResult = $installer->generateConfig($dbConfig);
                
                if ($configResult['success']) {
                    $_SESSION['admin_data'] = $adminData;
                    $success = true;
                } else {
                    $errors[] = $configResult['message'];
                }
            } else {
                $errors[] = $result['message'];
            }
        }
    }
}

// Get values from POST
$adminData = [
    'full_name' => $_POST['admin_name'] ?? '',
    'email' => $_POST['admin_email'] ?? '',
    'password' => '',
    'password_confirm' => ''
];
?>

<div class="admin-content">
    <h2>Create Administrator Account</h2>
    
    <p>Create your administrator account to manage ProjectManager. This will be the main account with full access to all features.</p>
    
    <?php if (!empty($errors)): ?>
        <div class="alert alert-danger">
            <strong>Please fix the following errors:</strong><br>
            <?php foreach ($errors as $error): ?>
                • <?php echo htmlspecialchars($error); ?><br>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
    
    <?php if ($success): ?>
        <div class="alert alert-success">
            <strong>✅ Administrator Account Created</strong><br>
            Your admin account has been created successfully and the configuration file has been generated. You're almost done!
        </div>
    <?php endif; ?>
    
    <form id="install-form" method="post">
        <div class="form-group">
            <label for="admin_name">Full Name</label>
            <input type="text" id="admin_name" name="admin_name" value="<?php echo htmlspecialchars($adminData['full_name']); ?>" required>
            <small>Your full name as it will appear in ProjectManager.</small>
        </div>
        
        <div class="form-group">
            <label for="admin_email">Email Address</label>
            <input type="email" id="admin_email" name="admin_email" value="<?php echo htmlspecialchars($adminData['email']); ?>" required>
            <small>This will be your login username and where notifications are sent.</small>
        </div>
        
        <div class="form-group">
            <label for="admin_password">Password</label>
            <input type="password" id="admin_password" name="admin_password" required minlength="8">
            <small>Choose a strong password with at least 8 characters.</small>
            <div id="password-strength" style="margin-top: 5px; font-size: 0.9rem;"></div>
        </div>
        
        <div class="form-group">
            <label for="admin_password_confirm">Confirm Password</label>
            <input type="password" id="admin_password_confirm" name="admin_password_confirm" required minlength="8">
            <small>Re-enter your password to confirm.</small>
        </div>
    </form>
    
    <div class="security-tips" style="margin-top: 30px; padding: 20px; background: #fff3cd; border-radius: 8px;">
        <h3>🔒 Security Tips</h3>
        <ul>
            <li><strong>Use a strong password:</strong> Include uppercase, lowercase, numbers, and special characters</li>
            <li><strong>Keep it unique:</strong> Don't reuse passwords from other accounts</li>
            <li><strong>Store it safely:</strong> Consider using a password manager</li>
            <li><strong>Enable 2FA:</strong> You can enable two-factor authentication after installation</li>
        </ul>
    </div>
    
    <div class="admin-privileges" style="margin-top: 20px; padding: 20px; background: #e3f2fd; border-radius: 8px;">
        <h3>👑 Administrator Privileges</h3>
        <p>As an administrator, you will have access to:</p>
        <ul>
            <li>All projects and tasks across the system</li>
            <li>User management and role assignments</li>
            <li>System settings and configuration</li>
            <li>Plugin and theme management</li>
            <li>Database backups and maintenance</li>
            <li>System reports and analytics</li>
        </ul>
    </div>
    
    <div class="next-steps-info" style="margin-top: 20px; padding: 15px; background: #d4edda; border-radius: 8px;">
        <h4>What happens next:</h4>
        <ol>
            <li>Your administrator account will be created</li>
            <li>The configuration file (config.php) will be generated</li>
            <li>All installation files will be secured</li>
            <li>You'll be ready to start using ProjectManager!</li>
        </ol>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('admin_password');
    const confirmInput = document.getElementById('admin_password_confirm');
    const strengthDiv = document.getElementById('password-strength');
    
    // Password strength checker
    passwordInput.addEventListener('input', function() {
        const password = this.value;
        const strength = checkPasswordStrength(password);
        
        strengthDiv.innerHTML = `Password strength: <span style="color: ${strength.color}; font-weight: bold;">${strength.text}</span>`;
    });
    
    // Password confirmation checker
    confirmInput.addEventListener('input', function() {
        const password = passwordInput.value;
        const confirm = this.value;
        
        if (confirm && password !== confirm) {
            this.setCustomValidity('Passwords do not match');
        } else {
            this.setCustomValidity('');
        }
    });
    
    function checkPasswordStrength(password) {
        let score = 0;
        
        if (password.length >= 8) score++;
        if (password.length >= 12) score++;
        if (/[a-z]/.test(password)) score++;
        if (/[A-Z]/.test(password)) score++;
        if (/[0-9]/.test(password)) score++;
        if (/[^A-Za-z0-9]/.test(password)) score++;
        
        if (score < 3) {
            return { text: 'Weak', color: '#dc3545' };
        } else if (score < 5) {
            return { text: 'Medium', color: '#ffc107' };
        } else {
            return { text: 'Strong', color: '#28a745' };
        }
    }
});
</script>
