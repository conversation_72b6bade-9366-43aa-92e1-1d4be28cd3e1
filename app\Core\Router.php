<?php
/**
 * Router Class
 * 
 * Handle URL routing and request dispatching
 * 
 * @package ProjectManager\Core
 */

declare(strict_types=1);

namespace App\Core;

use App\Controllers\BaseController;
use Exception;

/**
 * Router Class
 */
class Router
{
    /**
     * Registered routes
     * 
     * @var array
     */
    private array $routes = [];
    
    /**
     * Current route
     * 
     * @var string
     */
    private string $currentRoute = '';
    
    /**
     * Auth instance
     * 
     * @var Auth
     */
    private Auth $auth;
    
    /**
     * View instance
     * 
     * @var View
     */
    private View $view;
    
    /**
     * Router constructor
     * 
     * @param Auth $auth
     * @param View $view
     */
    public function __construct(Auth $auth, View $view)
    {
        $this->auth = $auth;
        $this->view = $view;
        $this->registerDefaultRoutes();
    }
    
    /**
     * Register default routes
     */
    private function registerDefaultRoutes(): void
    {
        // Public routes
        $this->addRoute('GET', '', 'DashboardController@index');
        $this->addRoute('GET', 'login', 'UserController@showLogin');
        $this->addRoute('POST', 'login', 'UserController@login');
        $this->addRoute('GET', 'register', 'UserController@showRegister');
        $this->addRoute('POST', 'register', 'UserController@register');
        $this->addRoute('GET', 'logout', 'UserController@logout');
        
        // Dashboard routes
        $this->addRoute('GET', 'dashboard', 'DashboardController@index');
        
        // Project routes
        $this->addRoute('GET', 'projects', 'ProjectController@index');
        $this->addRoute('GET', 'projects/create', 'ProjectController@create');
        $this->addRoute('POST', 'projects', 'ProjectController@store');
        $this->addRoute('GET', 'projects/{id}', 'ProjectController@show');
        $this->addRoute('GET', 'projects/{id}/edit', 'ProjectController@edit');
        $this->addRoute('PUT', 'projects/{id}', 'ProjectController@update');
        $this->addRoute('DELETE', 'projects/{id}', 'ProjectController@destroy');
        
        // Task routes
        $this->addRoute('GET', 'projects/{project_id}/tasks', 'TaskController@index');
        $this->addRoute('POST', 'projects/{project_id}/tasks', 'TaskController@store');
        $this->addRoute('GET', 'tasks/{id}', 'TaskController@show');
        $this->addRoute('PUT', 'tasks/{id}', 'TaskController@update');
        $this->addRoute('DELETE', 'tasks/{id}', 'TaskController@destroy');
        
        // API routes
        $this->addRoute('GET', 'api/projects', 'Api\\ProjectController@index');
        $this->addRoute('GET', 'api/tasks', 'Api\\TaskController@index');
        $this->addRoute('PUT', 'api/tasks/{id}/status', 'Api\\TaskController@updateStatus');
        
        // Admin routes
        $this->addRoute('GET', 'admin', 'Admin\\DashboardController@index');
        $this->addRoute('GET', 'admin/users', 'Admin\\UserController@index');
        $this->addRoute('GET', 'admin/settings', 'Admin\\SettingsController@index');
    }
    
    /**
     * Add a route
     * 
     * @param string $method
     * @param string $path
     * @param string $handler
     * @param array $middleware
     */
    public function addRoute(string $method, string $path, string $handler, array $middleware = []): void
    {
        $this->routes[] = [
            'method' => strtoupper($method),
            'path' => $path,
            'handler' => $handler,
            'middleware' => $middleware,
            'pattern' => $this->convertToPattern($path)
        ];
    }
    
    /**
     * Handle incoming request
     */
    public function handleRequest(): void
    {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = $this->getCurrentPath();
        
        // Handle method override for forms
        if ($method === 'POST' && isset($_POST['_method'])) {
            $method = strtoupper($_POST['_method']);
        }
        
        $this->currentRoute = $path;
        
        // Find matching route
        $route = $this->findRoute($method, $path);
        
        if ($route) {
            $this->executeRoute($route, $path);
        } else {
            $this->handle404();
        }
    }
    
    /**
     * Get current request path
     * 
     * @return string
     */
    private function getCurrentPath(): string
    {
        $path = $_GET['route'] ?? '';
        
        // Remove leading/trailing slashes
        $path = trim($path, '/');
        
        return $path;
    }
    
    /**
     * Convert route path to regex pattern
     * 
     * @param string $path
     * @return string
     */
    private function convertToPattern(string $path): string
    {
        // Convert {param} to named capture groups
        $pattern = preg_replace('/\{([^}]+)\}/', '(?P<$1>[^/]+)', $path);
        
        // Escape forward slashes
        $pattern = str_replace('/', '\/', $pattern);
        
        return '/^' . $pattern . '$/';
    }
    
    /**
     * Find matching route
     * 
     * @param string $method
     * @param string $path
     * @return array|null
     */
    private function findRoute(string $method, string $path): ?array
    {
        foreach ($this->routes as $route) {
            if ($route['method'] === $method && preg_match($route['pattern'], $path, $matches)) {
                // Extract parameters
                $params = [];
                foreach ($matches as $key => $value) {
                    if (!is_numeric($key)) {
                        $params[$key] = $value;
                    }
                }
                
                $route['params'] = $params;
                return $route;
            }
        }
        
        return null;
    }
    
    /**
     * Execute a route
     * 
     * @param array $route
     * @param string $path
     */
    private function executeRoute(array $route, string $path): void
    {
        try {
            // Apply middleware
            foreach ($route['middleware'] as $middleware) {
                $this->applyMiddleware($middleware);
            }
            
            // Parse handler
            [$controllerName, $method] = explode('@', $route['handler']);
            
            // Build full controller class name
            $controllerClass = 'App\\Controllers\\' . $controllerName;
            
            // Check if controller exists
            if (!class_exists($controllerClass)) {
                throw new Exception("Controller {$controllerClass} not found");
            }
            
            // Instantiate controller
            $controller = new $controllerClass($this->auth, $this->view);
            
            // Check if method exists
            if (!method_exists($controller, $method)) {
                throw new Exception("Method {$method} not found in {$controllerClass}");
            }
            
            // Call controller method with parameters
            $controller->$method($route['params'] ?? []);
            
        } catch (Exception $e) {
            $this->handleError($e);
        }
    }
    
    /**
     * Apply middleware
     * 
     * @param string $middleware
     */
    private function applyMiddleware(string $middleware): void
    {
        $middlewareClass = 'App\\Middlewares\\' . $middleware;
        
        if (class_exists($middlewareClass)) {
            $middlewareInstance = new $middlewareClass($this->auth);
            $middlewareInstance->handle();
        }
    }
    
    /**
     * Handle 404 errors
     */
    private function handle404(): void
    {
        http_response_code(404);
        $this->view->render('errors/404', [
            'path' => $this->currentRoute
        ]);
    }
    
    /**
     * Handle route errors
     * 
     * @param Exception $e
     */
    private function handleError(Exception $e): void
    {
        error_log('Router Error: ' . $e->getMessage());
        
        http_response_code(500);
        
        if (defined('PM_DEBUG') && PM_DEBUG) {
            $this->view->render('errors/debug', [
                'exception' => $e
            ]);
        } else {
            $this->view->render('errors/500', [
                'message' => 'An error occurred while processing your request.'
            ]);
        }
    }
    
    /**
     * Generate URL for a route
     * 
     * @param string $path
     * @param array $params
     * @return string
     */
    public function url(string $path, array $params = []): string
    {
        $url = rtrim(PM_SITE_URL, '/') . '/' . ltrim($path, '/');
        
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        
        return $url;
    }
    
    /**
     * Redirect to a URL
     * 
     * @param string $url
     * @param int $code
     */
    public function redirect(string $url, int $code = 302): void
    {
        header("Location: {$url}", true, $code);
        exit;
    }
    
    /**
     * Get current route
     * 
     * @return string
     */
    public function getCurrentRoute(): string
    {
        return $this->currentRoute;
    }
}
