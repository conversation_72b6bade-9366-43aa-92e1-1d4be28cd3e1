<?php
/**
 * View Rendering Class
 * 
 * Handle template rendering and output
 * 
 * @package ProjectManager\Core
 */

declare(strict_types=1);

namespace App\Core;

use Exception;

/**
 * View Class
 */
class View
{
    /**
     * Active theme
     * 
     * @var string
     */
    private string $activeTheme;
    
    /**
     * Global view data
     * 
     * @var array
     */
    private array $globalData = [];
    
    /**
     * View constructor
     */
    public function __construct()
    {
        $this->activeTheme = defined('PM_DEFAULT_THEME') ? PM_DEFAULT_THEME : 'default';
        $this->setupGlobalData();
    }
    
    /**
     * Setup global view data
     */
    private function setupGlobalData(): void
    {
        $this->globalData = [
            'site_url' => defined('PM_SITE_URL') ? PM_SITE_URL : '',
            'assets_url' => $this->getAssetsUrl(),
            'app_name' => 'ProjectManager',
            'app_version' => PM_VERSION,
            'current_year' => date('Y'),
        ];
    }
    
    /**
     * Render a template
     * 
     * @param string $template
     * @param array $data
     * @param bool $return
     * @return string|void
     */
    public function render(string $template, array $data = [], bool $return = false)
    {
        $templateFile = $this->findTemplate($template);
        
        if (!$templateFile) {
            throw new Exception("Template '{$template}' not found");
        }
        
        // Merge global data with template data
        $data = array_merge($this->globalData, $data);
        
        // Start output buffering
        ob_start();
        
        // Extract data to variables
        extract($data, EXTR_SKIP);
        
        // Include the template file
        include $templateFile;
        
        // Get the output
        $output = ob_get_clean();
        
        if ($return) {
            return $output;
        }
        
        echo $output;
    }
    
    /**
     * Find template file
     * 
     * @param string $template
     * @return string|null
     */
    private function findTemplate(string $template): ?string
    {
        $templatePaths = [
            // Theme template
            PM_THEMES_PATH . '/' . $this->activeTheme . '/templates/' . $template . '.php',
            // Default theme template
            PM_THEMES_PATH . '/default/templates/' . $template . '.php',
            // Core template
            PM_APP_PATH . '/Views/' . $template . '.php',
        ];
        
        foreach ($templatePaths as $path) {
            if (file_exists($path)) {
                return $path;
            }
        }
        
        return null;
    }
    
    /**
     * Render a partial template
     * 
     * @param string $partial
     * @param array $data
     * @return string
     */
    public function partial(string $partial, array $data = []): string
    {
        return $this->render('partials/' . $partial, $data, true);
    }
    
    /**
     * Include a template part
     * 
     * @param string $part
     * @param array $data
     */
    public function include(string $part, array $data = []): void
    {
        $this->render('parts/' . $part, $data);
    }
    
    /**
     * Escape HTML output
     * 
     * @param string $string
     * @return string
     */
    public function escape(string $string): string
    {
        return htmlspecialchars($string, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }
    
    /**
     * Escape HTML attributes
     * 
     * @param string $string
     * @return string
     */
    public function escapeAttr(string $string): string
    {
        return htmlspecialchars($string, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }
    
    /**
     * Escape URL
     * 
     * @param string $url
     * @return string
     */
    public function escapeUrl(string $url): string
    {
        return filter_var($url, FILTER_SANITIZE_URL);
    }
    
    /**
     * Generate URL
     * 
     * @param string $path
     * @param array $params
     * @return string
     */
    public function url(string $path = '', array $params = []): string
    {
        $url = rtrim($this->globalData['site_url'], '/');
        
        if ($path) {
            $url .= '/' . ltrim($path, '/');
        }
        
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        
        return $url;
    }
    
    /**
     * Generate asset URL
     * 
     * @param string $asset
     * @return string
     */
    public function asset(string $asset): string
    {
        return $this->globalData['assets_url'] . '/' . ltrim($asset, '/');
    }
    
    /**
     * Get assets URL
     * 
     * @return string
     */
    private function getAssetsUrl(): string
    {
        $baseUrl = defined('PM_SITE_URL') ? PM_SITE_URL : '';
        return rtrim($baseUrl, '/') . '/assets';
    }
    
    /**
     * Format date
     * 
     * @param string $date
     * @param string $format
     * @return string
     */
    public function formatDate(string $date, string $format = 'Y-m-d H:i:s'): string
    {
        return date($format, strtotime($date));
    }
    
    /**
     * Format relative time
     * 
     * @param string $date
     * @return string
     */
    public function timeAgo(string $date): string
    {
        $time = time() - strtotime($date);
        
        if ($time < 60) {
            return 'just now';
        } elseif ($time < 3600) {
            $minutes = floor($time / 60);
            return $minutes . ' minute' . ($minutes > 1 ? 's' : '') . ' ago';
        } elseif ($time < 86400) {
            $hours = floor($time / 3600);
            return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
        } elseif ($time < 2592000) {
            $days = floor($time / 86400);
            return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
        } else {
            return $this->formatDate($date, 'M j, Y');
        }
    }
    
    /**
     * Add global data
     * 
     * @param string $key
     * @param mixed $value
     */
    public function addGlobal(string $key, $value): void
    {
        $this->globalData[$key] = $value;
    }
    
    /**
     * Set active theme
     * 
     * @param string $theme
     */
    public function setTheme(string $theme): void
    {
        $this->activeTheme = $theme;
    }
    
    /**
     * Get active theme
     * 
     * @return string
     */
    public function getTheme(): string
    {
        return $this->activeTheme;
    }
    
    /**
     * Check if template exists
     * 
     * @param string $template
     * @return bool
     */
    public function templateExists(string $template): bool
    {
        return $this->findTemplate($template) !== null;
    }
    
    /**
     * Render JSON response
     * 
     * @param array $data
     * @param int $statusCode
     */
    public function json(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data, JSON_THROW_ON_ERROR);
        exit;
    }
    
    /**
     * Render error page
     * 
     * @param int $code
     * @param string $message
     */
    public function error(int $code, string $message = ''): void
    {
        http_response_code($code);
        
        $this->render("errors/{$code}", [
            'error_code' => $code,
            'error_message' => $message ?: $this->getDefaultErrorMessage($code)
        ]);
    }
    
    /**
     * Get default error message
     *
     * @param int $code
     * @return string
     */
    private function getDefaultErrorMessage(int $code): string
    {
        $messages = [
            400 => 'Bad Request',
            401 => 'Unauthorized',
            403 => 'Forbidden',
            404 => 'Page Not Found',
            500 => 'Internal Server Error',
            503 => 'Service Unavailable'
        ];

        return $messages[$code] ?? 'An error occurred';
    }

    /**
     * Get status color for badges
     *
     * @param string $status
     * @return string
     */
    public function getStatusColor(string $status): string
    {
        $colors = [
            'active' => 'success',
            'completed' => 'success',
            'done' => 'success',
            'inactive' => 'danger',
            'suspended' => 'danger',
            'pending' => 'warning',
            'todo' => 'warning',
            'in_progress' => 'info',
            'review' => 'info',
            'archived' => 'secondary',
            'on_hold' => 'secondary'
        ];

        return $colors[$status] ?? 'secondary';
    }

    /**
     * Get priority color for badges
     *
     * @param string $priority
     * @return string
     */
    public function getPriorityColor(string $priority): string
    {
        $colors = [
            'low' => 'secondary',
            'medium' => 'info',
            'high' => 'warning',
            'urgent' => 'danger'
        ];

        return $colors[$priority] ?? 'secondary';
    }

    /**
     * Check if user has permission (helper for templates)
     *
     * @param string $permission
     * @return bool
     */
    public function hasPermission(string $permission): bool
    {
        $app = Application::getInstance();
        return $app->getAuth()->hasPermission($permission);
    }

    /**
     * Get current route (helper for templates)
     *
     * @return string
     */
    public function getCurrentRoute(): string
    {
        $app = Application::getInstance();
        return $app->getRouter()->getCurrentRoute();
    }

    /**
     * Check if registration is enabled (helper for templates)
     *
     * @return bool
     */
    public function isRegistrationEnabled(): bool
    {
        return defined('PM_REGISTRATION_ENABLED') ? constant('PM_REGISTRATION_ENABLED') : true;
    }
}
