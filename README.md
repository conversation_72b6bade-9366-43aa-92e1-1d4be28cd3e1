# ProjectManager

A complete web-based project management application built with WordPress-style architecture, designed to run on shared hosting environments.

## Features

### Core Features
- **User Management**: Registration, login, profile management with role-based access control
- **Project Management**: Create, edit, delete, and organize projects with metadata
- **Task Management**: Task creation, assignment, status tracking, due dates
- **Dashboard**: Overview of user's projects and assigned tasks
- **Comments System**: Threaded comments on projects and tasks
- **File Management**: Secure file upload/download with validation
- **Tagging System**: Flexible tagging for projects and tasks

### Advanced Features
- **Kanban Board**: Visual task management with drag-and-drop
- **Email Notifications**: Configurable notifications using PHPMailer
- **Search**: Full-text search across projects, tasks, and comments
- **Reporting**: Dashboard with statistics and analytics
- **Plugin System**: WordPress-style plugin architecture
- **Theme System**: Customizable themes with template hierarchy

## Requirements

### Server Requirements
- **PHP**: 8.1 or higher
- **Database**: MySQL 5.7+ or MariaDB 10.3+
- **Web Server**: Apache with mod_rewrite (or Nginx)
- **Memory**: 128MB PHP memory limit minimum

### PHP Extensions
- PDO and PDO MySQL
- Mbstring
- JSON
- OpenSSL (recommended)
- cURL (recommended)
- GD (recommended)

## Installation

### Quick Installation
1. Download and extract ProjectManager to your web directory
2. Create a MySQL database for ProjectManager
3. Navigate to your website in a browser
4. Follow the installation wizard

### Manual Installation
1. Copy `config-sample.php` to `config.php`
2. Edit `config.php` with your database credentials
3. Import `install/database.sql` into your MySQL database
4. Create an admin user account
5. Set proper file permissions (755 for directories, 644 for files)

### File Permissions
```bash
# Set directory permissions
find . -type d -exec chmod 755 {} \;

# Set file permissions
find . -type f -exec chmod 644 {} \;

# Make storage directories writable
chmod -R 755 storage/
```

## Configuration

### Database Configuration
```php
// Database settings
define('DB_HOST', 'localhost');
define('DB_NAME', 'projectmanager');
define('DB_USER', 'username');
define('DB_PASSWORD', 'password');
```

### Security Configuration
```php
// Generate unique keys at: https://api.wordpress.org/secret-key/1.1/salt/
define('AUTH_KEY', 'your-unique-key-here');
define('SECURE_AUTH_KEY', 'your-unique-key-here');
// ... more keys
```

### Application Settings
```php
define('PM_DEBUG', false);
define('PM_SITE_URL', 'https://yoursite.com');
define('PM_ADMIN_EMAIL', '<EMAIL>');
```

## Directory Structure

```
/
├── index.php                 # Front controller
├── .htaccess                 # URL rewriting rules
├── config.php               # Configuration file
├── /app                      # Application core
│   ├── /Core                 # Core classes
│   ├── /Controllers          # Request controllers
│   ├── /Models               # Data models
│   └── /Views                # View templates
├── /themes                   # Theme system
│   └── /default              # Default theme
├── /plugins                  # Plugin system
├── /storage                  # File storage
│   ├── /cache                # Cache files
│   ├── /logs                 # Log files
│   ├── /uploads              # User uploads
│   └── /backups              # Backup files
├── /assets                   # Static assets
│   ├── /css                  # Stylesheets
│   ├── /js                   # JavaScript
│   └── /images               # Images
└── /install                  # Installation wizard
```

## Usage

### Creating Projects
1. Navigate to Projects → New Project
2. Fill in project details (name, description, dates)
3. Add team members and assign roles
4. Set project status and priority

### Managing Tasks
1. Open a project
2. Click "Add Task" to create new tasks
3. Assign tasks to team members
4. Set due dates and priorities
5. Track progress with status updates

### Using the Kanban Board
1. Navigate to a project
2. Switch to "Board" view
3. Drag and drop tasks between columns
4. Update task status by moving cards

## Development

### Plugin Development
Create a new plugin by adding a directory to `/plugins/`:

```php
<?php
/**
 * Plugin Name: My Custom Plugin
 * Description: A custom plugin for ProjectManager
 * Version: 1.0.0
 */

// Add action hooks
add_action('pm_init', 'my_plugin_init');

function my_plugin_init() {
    // Plugin initialization code
}
```

### Theme Development
Create a custom theme in `/themes/`:

```
/themes/my-theme/
├── style.css
├── functions.php
├── /templates/
│   ├── layout.php
│   ├── dashboard/
│   └── projects/
```

### Database Migrations
Create migration files in `/updates/migrations/`:

```sql
-- 002_add_custom_fields.sql
ALTER TABLE pm_projects ADD COLUMN custom_field VARCHAR(255);
```

## Security

### Best Practices
- Keep ProjectManager updated
- Use strong passwords
- Enable HTTPS
- Regular backups
- Monitor access logs
- Restrict file permissions

### Security Features
- CSRF protection
- XSS prevention
- SQL injection protection
- Secure file uploads
- Rate limiting
- Session security

## Troubleshooting

### Common Issues

**Installation fails with database error:**
- Check database credentials
- Ensure database exists
- Verify user permissions

**File upload not working:**
- Check storage directory permissions
- Verify PHP upload settings
- Check file size limits

**Performance issues:**
- Enable caching
- Optimize database
- Check server resources

### Debug Mode
Enable debug mode in `config.php`:
```php
define('PM_DEBUG', true);
```

## Support

### Documentation
- User Guide: `/docs/user-guide.md`
- Developer Guide: `/docs/developer-guide.md`
- API Reference: `/docs/api-reference.md`

### Community
- GitHub Issues: Report bugs and request features
- Community Forum: Get help from other users
- Documentation Wiki: Contribute to documentation

## License

ProjectManager is open-source software licensed under the MIT License.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## Changelog

### Version 1.0.0
- Initial release
- Core project and task management
- User authentication and authorization
- Plugin and theme system
- Installation wizard
- Basic reporting and dashboard

---

For more information, visit the [ProjectManager Documentation](docs/).
