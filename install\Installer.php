<?php
/**
 * ProjectManager Installer Class
 * 
 * Handle installation process
 * 
 * @package ProjectManager\Install
 */

declare(strict_types=1);

class Installer
{
    /**
     * Check system requirements
     * 
     * @return array
     */
    public function checkRequirements(): array
    {
        $requirements = [
            'php_version' => [
                'name' => 'PHP Version (8.1+)',
                'required' => true,
                'status' => version_compare(PHP_VERSION, '8.1.0', '>='),
                'current' => PHP_VERSION,
                'message' => version_compare(PHP_VERSION, '8.1.0', '>=') 
                    ? 'PHP version is compatible' 
                    : 'PHP 8.1 or higher is required'
            ],
            'pdo' => [
                'name' => 'PDO Extension',
                'required' => true,
                'status' => extension_loaded('pdo'),
                'current' => extension_loaded('pdo') ? 'Enabled' : 'Disabled',
                'message' => extension_loaded('pdo') 
                    ? 'PDO extension is available' 
                    : 'PDO extension is required for database connectivity'
            ],
            'pdo_mysql' => [
                'name' => 'PDO MySQL Driver',
                'required' => true,
                'status' => extension_loaded('pdo_mysql'),
                'current' => extension_loaded('pdo_mysql') ? 'Enabled' : 'Disabled',
                'message' => extension_loaded('pdo_mysql') 
                    ? 'MySQL PDO driver is available' 
                    : 'MySQL PDO driver is required'
            ],
            'mbstring' => [
                'name' => 'Mbstring Extension',
                'required' => true,
                'status' => extension_loaded('mbstring'),
                'current' => extension_loaded('mbstring') ? 'Enabled' : 'Disabled',
                'message' => extension_loaded('mbstring') 
                    ? 'Mbstring extension is available' 
                    : 'Mbstring extension is required for proper string handling'
            ],
            'json' => [
                'name' => 'JSON Extension',
                'required' => true,
                'status' => extension_loaded('json'),
                'current' => extension_loaded('json') ? 'Enabled' : 'Disabled',
                'message' => extension_loaded('json') 
                    ? 'JSON extension is available' 
                    : 'JSON extension is required'
            ],
            'openssl' => [
                'name' => 'OpenSSL Extension',
                'required' => false,
                'status' => extension_loaded('openssl'),
                'current' => extension_loaded('openssl') ? 'Enabled' : 'Disabled',
                'message' => extension_loaded('openssl') 
                    ? 'OpenSSL extension is available for enhanced security' 
                    : 'OpenSSL extension is recommended for enhanced security'
            ],
            'curl' => [
                'name' => 'cURL Extension',
                'required' => false,
                'status' => extension_loaded('curl'),
                'current' => extension_loaded('curl') ? 'Enabled' : 'Disabled',
                'message' => extension_loaded('curl') 
                    ? 'cURL extension is available for external requests' 
                    : 'cURL extension is recommended for external API calls'
            ],
            'gd' => [
                'name' => 'GD Extension',
                'required' => false,
                'status' => extension_loaded('gd'),
                'current' => extension_loaded('gd') ? 'Enabled' : 'Disabled',
                'message' => extension_loaded('gd') 
                    ? 'GD extension is available for image processing' 
                    : 'GD extension is recommended for image processing'
            ],
            'memory_limit' => [
                'name' => 'Memory Limit (128M+)',
                'required' => true,
                'status' => $this->checkMemoryLimit(),
                'current' => ini_get('memory_limit'),
                'message' => $this->checkMemoryLimit() 
                    ? 'Memory limit is sufficient' 
                    : 'Memory limit should be at least 128M'
            ],
            'max_execution_time' => [
                'name' => 'Max Execution Time (30s+)',
                'required' => false,
                'status' => $this->checkExecutionTime(),
                'current' => ini_get('max_execution_time') . 's',
                'message' => $this->checkExecutionTime() 
                    ? 'Execution time limit is sufficient' 
                    : 'Execution time limit should be at least 30 seconds'
            ]
        ];
        
        // Check file permissions
        $permissions = $this->checkFilePermissions();
        $requirements = array_merge($requirements, $permissions);
        
        return $requirements;
    }
    
    /**
     * Check memory limit
     * 
     * @return bool
     */
    private function checkMemoryLimit(): bool
    {
        $memoryLimit = ini_get('memory_limit');
        
        if ($memoryLimit === '-1') {
            return true; // No limit
        }
        
        $memoryLimitBytes = $this->convertToBytes($memoryLimit);
        return $memoryLimitBytes >= 128 * 1024 * 1024; // 128MB
    }
    
    /**
     * Check execution time
     * 
     * @return bool
     */
    private function checkExecutionTime(): bool
    {
        $maxExecutionTime = (int) ini_get('max_execution_time');
        return $maxExecutionTime === 0 || $maxExecutionTime >= 30;
    }
    
    /**
     * Convert memory limit to bytes
     * 
     * @param string $value
     * @return int
     */
    private function convertToBytes(string $value): int
    {
        $value = trim($value);
        $last = strtolower($value[strlen($value) - 1]);
        $value = (int) $value;
        
        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }
        
        return $value;
    }
    
    /**
     * Check file permissions
     * 
     * @return array
     */
    private function checkFilePermissions(): array
    {
        $permissions = [];
        $directories = [
            'storage' => PM_ROOT . '/storage',
            'storage/cache' => PM_ROOT . '/storage/cache',
            'storage/logs' => PM_ROOT . '/storage/logs',
            'storage/uploads' => PM_ROOT . '/storage/uploads',
            'storage/backups' => PM_ROOT . '/storage/backups'
        ];
        
        foreach ($directories as $name => $path) {
            $writable = $this->isWritable($path);
            $permissions["perm_{$name}"] = [
                'name' => "Write Permission: {$name}/",
                'required' => true,
                'status' => $writable,
                'current' => $writable ? 'Writable' : 'Not Writable',
                'message' => $writable 
                    ? "Directory {$name}/ is writable" 
                    : "Directory {$name}/ must be writable"
            ];
        }
        
        // Check if config.php can be created
        $configWritable = is_writable(PM_ROOT);
        $permissions['perm_config'] = [
            'name' => 'Write Permission: Root Directory',
            'required' => true,
            'status' => $configWritable,
            'current' => $configWritable ? 'Writable' : 'Not Writable',
            'message' => $configWritable 
                ? 'Root directory is writable for config.php creation' 
                : 'Root directory must be writable to create config.php'
        ];
        
        return $permissions;
    }
    
    /**
     * Check if directory is writable
     * 
     * @param string $path
     * @return bool
     */
    private function isWritable(string $path): bool
    {
        if (!is_dir($path)) {
            // Try to create the directory
            if (!mkdir($path, 0755, true)) {
                return false;
            }
        }
        
        return is_writable($path);
    }
    
    /**
     * Test database connection
     * 
     * @param array $config
     * @return array
     */
    public function testDatabaseConnection(array $config): array
    {
        try {
            $dsn = "mysql:host={$config['host']};charset=utf8mb4";
            $pdo = new PDO($dsn, $config['username'], $config['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            // Check if database exists
            $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
            $stmt->execute([$config['database']]);
            $dbExists = $stmt->fetch() !== false;
            
            if (!$dbExists) {
                // Try to create database
                $pdo->exec("CREATE DATABASE `{$config['database']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            }
            
            // Test connection to the specific database
            $dsn = "mysql:host={$config['host']};dbname={$config['database']};charset=utf8mb4";
            $pdo = new PDO($dsn, $config['username'], $config['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            return [
                'success' => true,
                'message' => 'Database connection successful'
            ];
            
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'Database connection failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Install database schema
     * 
     * @param array $config
     * @return array
     */
    public function installDatabase(array $config): array
    {
        try {
            $dsn = "mysql:host={$config['host']};dbname={$config['database']};charset=utf8mb4";
            $pdo = new PDO($dsn, $config['username'], $config['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            // Read SQL file
            $sqlFile = PM_ROOT . '/install/database.sql';
            $sql = file_get_contents($sqlFile);
            
            if ($sql === false) {
                throw new Exception('Could not read database.sql file');
            }
            
            // Replace table prefix
            $tablePrefix = $config['table_prefix'] ?? 'pm_';
            $sql = str_replace('{PREFIX}', $tablePrefix, $sql);
            
            // Execute SQL statements
            $pdo->exec($sql);
            
            return [
                'success' => true,
                'message' => 'Database schema installed successfully'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Database installation failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Create admin user
     * 
     * @param array $config
     * @param array $adminData
     * @return array
     */
    public function createAdminUser(array $config, array $adminData): array
    {
        try {
            $dsn = "mysql:host={$config['host']};dbname={$config['database']};charset=utf8mb4";
            $pdo = new PDO($dsn, $config['username'], $config['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            $tablePrefix = $config['table_prefix'] ?? 'pm_';
            
            // Hash password
            $hashedPassword = password_hash($adminData['password'], PASSWORD_ARGON2ID);
            
            // Insert admin user
            $stmt = $pdo->prepare("
                INSERT INTO `{$tablePrefix}users` 
                (email, password, full_name, status, email_verified_at) 
                VALUES (?, ?, ?, 'active', NOW())
            ");
            
            $stmt->execute([
                $adminData['email'],
                $hashedPassword,
                $adminData['full_name']
            ]);
            
            $userId = $pdo->lastInsertId();
            
            // Assign admin role
            $stmt = $pdo->prepare("
                SELECT id FROM `{$tablePrefix}user_roles` WHERE name = 'admin'
            ");
            $stmt->execute();
            $adminRole = $stmt->fetch();
            
            if ($adminRole) {
                $stmt = $pdo->prepare("
                    INSERT INTO `{$tablePrefix}user_role_assignments` 
                    (user_id, role_id) VALUES (?, ?)
                ");
                $stmt->execute([$userId, $adminRole['id']]);
            }
            
            return [
                'success' => true,
                'message' => 'Admin user created successfully'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Admin user creation failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Generate configuration file
     * 
     * @param array $config
     * @return array
     */
    public function generateConfig(array $config): array
    {
        try {
            $template = file_get_contents(PM_ROOT . '/config-sample.php');
            
            if ($template === false) {
                throw new Exception('Could not read config-sample.php');
            }
            
            // Replace placeholders
            $replacements = [
                'localhost' => $config['host'],
                'projectmanager' => $config['database'],
                'username' => $config['username'],
                'password' => $config['password'],
                'put your unique phrase here' => $this->generateSalt()
            ];
            
            foreach ($replacements as $search => $replace) {
                $template = str_replace($search, $replace, $template);
            }
            
            // Set table prefix
            if (isset($config['table_prefix'])) {
                $template = str_replace('$table_prefix = \'pm_\';', '$table_prefix = \'' . $config['table_prefix'] . '\';', $template);
            }
            
            // Write config file
            $configFile = PM_ROOT . '/config.php';
            if (file_put_contents($configFile, $template) === false) {
                throw new Exception('Could not write config.php file');
            }
            
            return [
                'success' => true,
                'message' => 'Configuration file created successfully'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Configuration file creation failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Generate random salt
     * 
     * @return string
     */
    private function generateSalt(): string
    {
        return bin2hex(random_bytes(32));
    }
}
