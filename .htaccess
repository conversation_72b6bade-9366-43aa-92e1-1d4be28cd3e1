# ProjectManager - URL Rewriting Rules
# Optimized for shared hosting environments

# Enable URL rewriting
RewriteEngine On

# Security: Block access to sensitive files and directories
<FilesMatch "^(config\.php|\.env|composer\.(json|lock)|package\.json)$">
    Order allow,deny
    <PERSON><PERSON> from all
</FilesMatch>

# Block access to storage directory (except uploads if served directly)
RewriteRule ^storage/(?!uploads/) - [F,L]

# Block access to app directory
RewriteRule ^app/ - [F,L]

# Block access to install directory after installation
<IfModule mod_rewrite.c>
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} ^/install/
    RewriteCond %{DOCUMENT_ROOT}/config.php -f
    RewriteRule ^install/ - [F,L]
</IfModule>

# Main rewrite rules - route everything through index.php
<IfModule mod_rewrite.c>
    # Handle installation requests
    RewriteCond %{REQUEST_URI} ^/install/
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^install/(.*)$ install/index.php [QSA,L]
    
    # Handle API requests
    RewriteCond %{REQUEST_URI} ^/api/
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^api/(.*)$ index.php?route=api/$1 [QSA,L]
    
    # Handle admin requests
    RewriteCond %{REQUEST_URI} ^/admin/
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^admin/(.*)$ index.php?route=admin/$1 [QSA,L]
    
    # Handle all other requests
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ index.php?route=$1 [QSA,L]
</IfModule>

# Fallback for servers without mod_rewrite
<IfModule !mod_rewrite.c>
    # Redirect to query string based routing
    ErrorDocument 404 /index.php
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # HSTS for HTTPS sites
    Header always set Strict-Transport-Security "max-age=********; includeSubDomains" env=HTTPS
</IfModule>

# Optimize file serving
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
