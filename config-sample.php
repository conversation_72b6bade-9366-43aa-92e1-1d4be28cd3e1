<?php
/**
 * ProjectManager Configuration Template
 * 
 * Copy this file to config.php and update the settings below
 * 
 * @package ProjectManager
 */

declare(strict_types=1);

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'projectmanager');
define('DB_USER', 'username');
define('DB_PASSWORD', 'password');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', 'utf8mb4_unicode_ci');

// Security Keys and Salts
// Generate unique keys at: https://api.wordpress.org/secret-key/1.1/salt/
define('AUTH_KEY',         'put your unique phrase here');
define('SECURE_AUTH_KEY',  'put your unique phrase here');
define('LOGGED_IN_KEY',    'put your unique phrase here');
define('NONCE_KEY',        'put your unique phrase here');
define('AUTH_SALT',        'put your unique phrase here');
define('SECURE_AUTH_SALT', 'put your unique phrase here');
define('LOGGED_IN_SALT',   'put your unique phrase here');
define('NONCE_SALT',       'put your unique phrase here');

// Application Settings
define('PM_DEBUG', false);
define('PM_ENVIRONMENT', 'production'); // development, staging, production
define('PM_SITE_URL', 'https://yoursite.com');
define('PM_ADMIN_EMAIL', '<EMAIL>');

// Session Configuration
define('PM_SESSION_LIFETIME', 7200); // 2 hours in seconds
define('PM_COOKIE_DOMAIN', '');
define('PM_COOKIE_PATH', '/');
define('PM_COOKIE_SECURE', false); // Set to true for HTTPS
define('PM_COOKIE_HTTPONLY', true);

// File Upload Settings
define('PM_MAX_UPLOAD_SIZE', 10485760); // 10MB in bytes
define('PM_ALLOWED_FILE_TYPES', 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip');

// Email Configuration
define('PM_MAIL_FROM_EMAIL', '<EMAIL>');
define('PM_MAIL_FROM_NAME', 'ProjectManager');
define('PM_MAIL_METHOD', 'mail'); // mail, smtp
define('PM_SMTP_HOST', '');
define('PM_SMTP_PORT', 587);
define('PM_SMTP_USERNAME', '');
define('PM_SMTP_PASSWORD', '');
define('PM_SMTP_ENCRYPTION', 'tls'); // tls, ssl

// Cache Settings
define('PM_CACHE_ENABLED', true);
define('PM_CACHE_LIFETIME', 3600); // 1 hour in seconds

// Timezone
define('PM_TIMEZONE', 'UTC');

// Language
define('PM_LANGUAGE', 'en_US');

// Theme and Plugin Settings
define('PM_DEFAULT_THEME', 'default');
define('PM_ALLOW_PLUGINS', true);

// Rate Limiting
define('PM_LOGIN_ATTEMPTS', 5);
define('PM_LOGIN_LOCKOUT_TIME', 900); // 15 minutes in seconds

// Pagination
define('PM_ITEMS_PER_PAGE', 20);

// API Settings
define('PM_API_ENABLED', true);
define('PM_API_RATE_LIMIT', 100); // requests per hour per IP

// Backup Settings
define('PM_AUTO_BACKUP', true);
define('PM_BACKUP_RETENTION_DAYS', 30);

// Logging
define('PM_LOG_LEVEL', 'error'); // debug, info, warning, error
define('PM_LOG_MAX_FILES', 10);

// Performance Settings
define('PM_MEMORY_LIMIT', '128M');
define('PM_MAX_EXECUTION_TIME', 30);

// Security Settings
define('PM_FORCE_SSL_ADMIN', false);
define('PM_DISABLE_FILE_EDIT', false);
define('PM_AUTOMATIC_UPDATER_DISABLED', false);

// Content Security Policy
define('PM_CSP_ENABLED', true);
define('PM_CSP_REPORT_ONLY', false);

// Two-Factor Authentication
define('PM_2FA_ENABLED', true);
define('PM_2FA_REQUIRED_FOR_ADMIN', false);

// Maintenance Mode
define('PM_MAINTENANCE_MODE', false);
define('PM_MAINTENANCE_MESSAGE', 'Site is temporarily unavailable for maintenance.');

// Custom table prefix (optional)
$table_prefix = 'pm_';

// That's all, stop editing! Happy project managing!
