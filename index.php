<?php
/**
 * ProjectManager - WordPress-style Project Management Application
 * 
 * Front Controller - All requests are routed through this file
 * 
 * @package ProjectManager
 * @version 1.0.0
 */

declare(strict_types=1);

// Define application constants
define('PM_VERSION', '1.0.0');
define('PM_ROOT', __DIR__);
define('PM_APP_PATH', PM_ROOT . '/app');
define('PM_STORAGE_PATH', PM_ROOT . '/storage');
define('PM_THEMES_PATH', PM_ROOT . '/themes');
define('PM_PLUGINS_PATH', PM_ROOT . '/plugins');
define('PM_MODULES_PATH', PM_ROOT . '/modules');
define('PM_ASSETS_PATH', PM_ROOT . '/assets');

// Check if installation is required
if (!file_exists(PM_ROOT . '/config.php') && strpos($_SERVER['REQUEST_URI'], '/install/') === false) {
    header('Location: /install/');
    exit;
}

// Load configuration if it exists
if (file_exists(PM_ROOT . '/config.php')) {
    require_once PM_ROOT . '/config.php';
}

// Load the autoloader
require_once PM_APP_PATH . '/Core/Autoloader.php';

// Initialize the application
try {
    $app = new \App\Core\Application();
    $app->run();
} catch (Exception $e) {
    // Handle fatal errors gracefully
    if (defined('PM_DEBUG') && PM_DEBUG) {
        echo '<h1>Application Error</h1>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
    } else {
        echo '<h1>Service Temporarily Unavailable</h1>';
        echo '<p>We are experiencing technical difficulties. Please try again later.</p>';
    }
    
    // Log the error
    error_log('ProjectManager Fatal Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());
}
