/**
 * ProjectManager Application JavaScript
 * 
 * Main application JavaScript file
 */

(function() {
    'use strict';

    // Application namespace
    window.ProjectManager = window.ProjectManager || {};

    // Configuration
    const config = {
        csrfToken: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
        apiUrl: '/api',
        debounceDelay: 300,
        animationDuration: 300
    };

    // Utility functions
    const utils = {
        /**
         * Debounce function
         */
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        /**
         * Show loading spinner
         */
        showLoading: function(element) {
            if (element) {
                element.innerHTML = '<span class="spinner"></span> Loading...';
                element.disabled = true;
            }
        },

        /**
         * Hide loading spinner
         */
        hideLoading: function(element, originalText) {
            if (element) {
                element.innerHTML = originalText || element.innerHTML.replace(/<span class="spinner"><\/span>\s*Loading\.\.\./, '');
                element.disabled = false;
            }
        },

        /**
         * Show toast notification
         */
        showToast: function(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(toast);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 5000);
        },

        /**
         * Format date
         */
        formatDate: function(date, format = 'Y-m-d') {
            const d = new Date(date);
            const year = d.getFullYear();
            const month = String(d.getMonth() + 1).padStart(2, '0');
            const day = String(d.getDate()).padStart(2, '0');
            
            switch (format) {
                case 'Y-m-d':
                    return `${year}-${month}-${day}`;
                case 'M j, Y':
                    return d.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
                default:
                    return d.toLocaleDateString();
            }
        },

        /**
         * Validate email
         */
        isValidEmail: function(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },

        /**
         * Get URL parameter
         */
        getUrlParameter: function(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }
    };

    // AJAX helper
    const ajax = {
        /**
         * Make AJAX request
         */
        request: function(url, options = {}) {
            const defaults = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            };

            // Add CSRF token for non-GET requests
            if (config.csrfToken && options.method !== 'GET') {
                defaults.headers['X-CSRF-Token'] = config.csrfToken;
            }

            const settings = Object.assign({}, defaults, options);
            
            return fetch(url, settings)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .catch(error => {
                    console.error('AJAX request failed:', error);
                    throw error;
                });
        },

        /**
         * GET request
         */
        get: function(url) {
            return this.request(url);
        },

        /**
         * POST request
         */
        post: function(url, data) {
            return this.request(url, {
                method: 'POST',
                body: JSON.stringify(data)
            });
        },

        /**
         * PUT request
         */
        put: function(url, data) {
            return this.request(url, {
                method: 'PUT',
                body: JSON.stringify(data)
            });
        },

        /**
         * DELETE request
         */
        delete: function(url) {
            return this.request(url, {
                method: 'DELETE'
            });
        }
    };

    // Form handling
    const forms = {
        /**
         * Initialize form validation
         */
        initValidation: function() {
            const forms = document.querySelectorAll('.needs-validation');
            
            forms.forEach(form => {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                });
            });
        },

        /**
         * Handle AJAX form submission
         */
        handleAjaxSubmit: function(form, callback) {
            form.addEventListener('submit', function(event) {
                event.preventDefault();
                
                const formData = new FormData(form);
                const data = Object.fromEntries(formData.entries());
                const submitBtn = form.querySelector('button[type="submit"]');
                const originalText = submitBtn?.innerHTML;
                
                utils.showLoading(submitBtn);
                
                ajax.post(form.action, data)
                    .then(response => {
                        if (response.success) {
                            utils.showToast(response.message, 'success');
                            if (callback) callback(response);
                        } else {
                            utils.showToast(response.message, 'danger');
                        }
                    })
                    .catch(error => {
                        utils.showToast('An error occurred. Please try again.', 'danger');
                    })
                    .finally(() => {
                        utils.hideLoading(submitBtn, originalText);
                    });
            });
        }
    };

    // UI components
    const ui = {
        /**
         * Initialize tooltips
         */
        initTooltips: function() {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        },

        /**
         * Initialize popovers
         */
        initPopovers: function() {
            const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            popoverTriggerList.map(function(popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
        },

        /**
         * Initialize modals
         */
        initModals: function() {
            // Auto-focus first input in modals
            document.addEventListener('shown.bs.modal', function(event) {
                const modal = event.target;
                const firstInput = modal.querySelector('input, textarea, select');
                if (firstInput) {
                    firstInput.focus();
                }
            });
        },

        /**
         * Initialize search functionality
         */
        initSearch: function() {
            const searchInputs = document.querySelectorAll('[data-search]');
            
            searchInputs.forEach(input => {
                const target = input.getAttribute('data-search');
                const targetElements = document.querySelectorAll(target);
                
                const searchFunction = utils.debounce(function() {
                    const query = input.value.toLowerCase();
                    
                    targetElements.forEach(element => {
                        const text = element.textContent.toLowerCase();
                        const shouldShow = text.includes(query);
                        element.style.display = shouldShow ? '' : 'none';
                    });
                }, config.debounceDelay);
                
                input.addEventListener('input', searchFunction);
            });
        },

        /**
         * Initialize confirmation dialogs
         */
        initConfirmations: function() {
            document.addEventListener('click', function(event) {
                const element = event.target.closest('[data-confirm]');
                if (element) {
                    const message = element.getAttribute('data-confirm');
                    if (!confirm(message)) {
                        event.preventDefault();
                        return false;
                    }
                }
            });
        }
    };

    // Application initialization
    const app = {
        /**
         * Initialize the application
         */
        init: function() {
            // Initialize UI components
            ui.initTooltips();
            ui.initPopovers();
            ui.initModals();
            ui.initSearch();
            ui.initConfirmations();
            
            // Initialize forms
            forms.initValidation();
            
            // Initialize AJAX forms
            const ajaxForms = document.querySelectorAll('.ajax-form');
            ajaxForms.forEach(form => {
                forms.handleAjaxSubmit(form);
            });
            
            // Auto-dismiss alerts
            setTimeout(() => {
                const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
                alerts.forEach(alert => {
                    if (alert.querySelector('.btn-close')) {
                        alert.querySelector('.btn-close').click();
                    }
                });
            }, 5000);
            
            console.log('ProjectManager application initialized');
        }
    };

    // Expose public API
    ProjectManager.utils = utils;
    ProjectManager.ajax = ajax;
    ProjectManager.forms = forms;
    ProjectManager.ui = ui;
    ProjectManager.config = config;

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', app.init);
    } else {
        app.init();
    }

})();
