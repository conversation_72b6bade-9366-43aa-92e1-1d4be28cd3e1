<?php
$errors = [];
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $dbConfig = [
        'host' => $_POST['db_host'] ?? '',
        'database' => $_POST['db_name'] ?? '',
        'username' => $_POST['db_user'] ?? '',
        'password' => $_POST['db_password'] ?? '',
        'table_prefix' => $_POST['table_prefix'] ?? 'pm_'
    ];
    
    // Validate input
    if (empty($dbConfig['host'])) {
        $errors[] = 'Database host is required';
    }
    if (empty($dbConfig['database'])) {
        $errors[] = 'Database name is required';
    }
    if (empty($dbConfig['username'])) {
        $errors[] = 'Database username is required';
    }
    
    if (empty($errors)) {
        // Test database connection
        $result = $installer->testDatabaseConnection($dbConfig);
        
        if ($result['success']) {
            // Install database schema
            $installResult = $installer->installDatabase($dbConfig);
            
            if ($installResult['success']) {
                // Store config in session for next step
                $_SESSION['db_config'] = $dbConfig;
                $success = true;
            } else {
                $errors[] = $installResult['message'];
            }
        } else {
            $errors[] = $result['message'];
        }
    }
}

// Get values from session or POST
$dbConfig = $_SESSION['db_config'] ?? [
    'host' => $_POST['db_host'] ?? 'localhost',
    'database' => $_POST['db_name'] ?? '',
    'username' => $_POST['db_user'] ?? '',
    'password' => $_POST['db_password'] ?? '',
    'table_prefix' => $_POST['table_prefix'] ?? 'pm_'
];
?>

<div class="database-content">
    <h2>Database Configuration</h2>
    
    <p>Please provide your database connection details. If you don't have a database yet, you'll need to create one through your hosting control panel.</p>
    
    <?php if (!empty($errors)): ?>
        <div class="alert alert-danger">
            <strong>Database Configuration Error</strong><br>
            <?php foreach ($errors as $error): ?>
                • <?php echo htmlspecialchars($error); ?><br>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
    
    <?php if ($success): ?>
        <div class="alert alert-success">
            <strong>✅ Database Connected Successfully</strong><br>
            Database connection established and schema installed. You can proceed to create your admin account.
        </div>
    <?php endif; ?>
    
    <form id="install-form" method="post">
        <div class="form-group">
            <label for="db_host">Database Host</label>
            <input type="text" id="db_host" name="db_host" value="<?php echo htmlspecialchars($dbConfig['host']); ?>" required>
            <small>Usually "localhost" for shared hosting. Check with your hosting provider if unsure.</small>
        </div>
        
        <div class="form-group">
            <label for="db_name">Database Name</label>
            <input type="text" id="db_name" name="db_name" value="<?php echo htmlspecialchars($dbConfig['database']); ?>" required>
            <small>The name of the database you created for ProjectManager.</small>
        </div>
        
        <div class="form-group">
            <label for="db_user">Database Username</label>
            <input type="text" id="db_user" name="db_user" value="<?php echo htmlspecialchars($dbConfig['username']); ?>" required>
            <small>Your database username (may be the same as database name on shared hosting).</small>
        </div>
        
        <div class="form-group">
            <label for="db_password">Database Password</label>
            <input type="password" id="db_password" name="db_password" value="<?php echo htmlspecialchars($dbConfig['password']); ?>">
            <small>Your database password. Leave blank if no password is set.</small>
        </div>
        
        <div class="form-group">
            <label for="table_prefix">Table Prefix</label>
            <input type="text" id="table_prefix" name="table_prefix" value="<?php echo htmlspecialchars($dbConfig['table_prefix']); ?>" pattern="^[a-zA-Z][a-zA-Z0-9_]*$">
            <small>Prefix for database tables. Useful if you're sharing the database with other applications.</small>
        </div>
    </form>
    
    <div class="database-help" style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
        <h3>Need Help?</h3>
        
        <div style="margin: 15px 0;">
            <h4>Creating a Database:</h4>
            <ul>
                <li><strong>cPanel:</strong> Go to "MySQL Databases" and create a new database</li>
                <li><strong>Plesk:</strong> Go to "Databases" and click "Add Database"</li>
                <li><strong>phpMyAdmin:</strong> Click "New" and enter your database name</li>
                <li><strong>Command Line:</strong> <code>CREATE DATABASE your_database_name;</code></li>
            </ul>
        </div>
        
        <div style="margin: 15px 0;">
            <h4>Common Database Hosts:</h4>
            <ul>
                <li><strong>Shared Hosting:</strong> Usually "localhost"</li>
                <li><strong>Some Hosts:</strong> "mysql.yourdomain.com" or similar</li>
                <li><strong>Local Development:</strong> "localhost" or "127.0.0.1"</li>
            </ul>
        </div>
        
        <div style="margin: 15px 0;">
            <h4>Troubleshooting:</h4>
            <ul>
                <li>Double-check your database credentials in your hosting control panel</li>
                <li>Make sure the database user has full permissions on the database</li>
                <li>Verify that the database server is running and accessible</li>
                <li>Contact your hosting provider if you're still having issues</li>
            </ul>
        </div>
    </div>
    
    <div class="connection-test" style="margin-top: 20px; padding: 15px; background: #e3f2fd; border-radius: 8px;">
        <h4>What happens when you click Continue:</h4>
        <ol>
            <li>We'll test the connection to your database server</li>
            <li>We'll create the database if it doesn't exist (if permissions allow)</li>
            <li>We'll install all the necessary tables and initial data</li>
            <li>We'll prepare your database for ProjectManager</li>
        </ol>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate table prefix based on database name
    const dbNameInput = document.getElementById('db_name');
    const prefixInput = document.getElementById('table_prefix');
    
    dbNameInput.addEventListener('input', function() {
        if (prefixInput.value === 'pm_' || prefixInput.value === '') {
            const dbName = this.value.toLowerCase().replace(/[^a-z0-9]/g, '');
            if (dbName) {
                prefixInput.value = dbName.substring(0, 8) + '_';
            } else {
                prefixInput.value = 'pm_';
            }
        }
    });
    
    // Validate table prefix
    prefixInput.addEventListener('input', function() {
        const value = this.value;
        const isValid = /^[a-zA-Z][a-zA-Z0-9_]*$/.test(value);
        
        if (!isValid && value !== '') {
            this.setCustomValidity('Table prefix must start with a letter and contain only letters, numbers, and underscores');
        } else {
            this.setCustomValidity('');
        }
    });
});
</script>
