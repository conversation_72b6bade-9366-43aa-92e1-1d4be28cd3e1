<?php
/**
 * Dashboard Controller
 * 
 * Handle dashboard and home page requests
 * 
 * @package ProjectManager\Controllers
 */

declare(strict_types=1);

namespace App\Controllers;

/**
 * Dashboard Controller Class
 */
class DashboardController extends BaseController
{
    /**
     * Show dashboard
     * 
     * @param array $params
     */
    public function index(array $params = []): void
    {
        // Redirect to login if not authenticated
        if (!$this->auth->isLoggedIn()) {
            $this->redirectToLogin();
            return;
        }
        
        // Get dashboard data
        $dashboardData = $this->getDashboardData();
        
        // Render dashboard
        $this->view->render('dashboard/index', [
            'page_title' => 'Dashboard',
            'dashboard_data' => $dashboardData,
            'flash_messages' => $this->getFlash()
        ]);
    }
    
    /**
     * Get dashboard data
     * 
     * @return array
     */
    private function getDashboardData(): array
    {
        $userId = $this->auth->getCurrentUserId();
        
        // Get user's projects
        $projects = $this->db->query("
            SELECT p.*, 
                   COUNT(DISTINCT t.id) as total_tasks,
                   COUNT(DISTINCT CASE WHEN t.status = 'done' THEN t.id END) as completed_tasks
            FROM {$this->db->getTable('projects')} p
            LEFT JOIN {$this->db->getTable('project_members')} pm ON p.id = pm.project_id
            LEFT JOIN {$this->db->getTable('tasks')} t ON p.id = t.project_id AND t.deleted_at IS NULL
            WHERE (p.owner_id = ? OR pm.user_id = ?) 
            AND p.deleted_at IS NULL
            GROUP BY p.id
            ORDER BY p.updated_at DESC
            LIMIT 5
        ", [$userId, $userId]);
        
        // Get user's recent tasks
        $recentTasks = $this->db->query("
            SELECT t.*, p.name as project_name
            FROM {$this->db->getTable('tasks')} t
            JOIN {$this->db->getTable('projects')} p ON t.project_id = p.id
            WHERE t.assigned_to = ? AND t.deleted_at IS NULL
            ORDER BY t.updated_at DESC
            LIMIT 10
        ", [$userId]);
        
        // Get user's pending tasks
        $pendingTasks = $this->db->query("
            SELECT t.*, p.name as project_name
            FROM {$this->db->getTable('tasks')} t
            JOIN {$this->db->getTable('projects')} p ON t.project_id = p.id
            WHERE t.assigned_to = ? 
            AND t.status IN ('todo', 'in_progress') 
            AND t.deleted_at IS NULL
            ORDER BY 
                CASE WHEN t.due_date IS NOT NULL THEN t.due_date END ASC,
                t.priority DESC,
                t.created_at ASC
            LIMIT 5
        ", [$userId]);
        
        // Get overdue tasks
        $overdueTasks = $this->db->query("
            SELECT t.*, p.name as project_name
            FROM {$this->db->getTable('tasks')} t
            JOIN {$this->db->getTable('projects')} p ON t.project_id = p.id
            WHERE t.assigned_to = ? 
            AND t.status NOT IN ('done') 
            AND t.due_date < NOW()
            AND t.deleted_at IS NULL
            ORDER BY t.due_date ASC
        ", [$userId]);
        
        // Get statistics
        $stats = [
            'total_projects' => $this->db->queryOne("
                SELECT COUNT(*) as count
                FROM {$this->db->getTable('projects')} p
                LEFT JOIN {$this->db->getTable('project_members')} pm ON p.id = pm.project_id
                WHERE (p.owner_id = ? OR pm.user_id = ?) 
                AND p.deleted_at IS NULL
            ", [$userId, $userId])['count'] ?? 0,
            
            'total_tasks' => $this->db->queryOne("
                SELECT COUNT(*) as count
                FROM {$this->db->getTable('tasks')} t
                WHERE t.assigned_to = ? AND t.deleted_at IS NULL
            ", [$userId])['count'] ?? 0,
            
            'completed_tasks' => $this->db->queryOne("
                SELECT COUNT(*) as count
                FROM {$this->db->getTable('tasks')} t
                WHERE t.assigned_to = ? 
                AND t.status = 'done' 
                AND t.deleted_at IS NULL
            ", [$userId])['count'] ?? 0,
            
            'overdue_tasks' => count($overdueTasks)
        ];
        
        // Calculate completion percentage
        $stats['completion_percentage'] = $stats['total_tasks'] > 0 
            ? round(($stats['completed_tasks'] / $stats['total_tasks']) * 100, 1)
            : 0;
        
        return [
            'projects' => $projects,
            'recent_tasks' => $recentTasks,
            'pending_tasks' => $pendingTasks,
            'overdue_tasks' => $overdueTasks,
            'stats' => $stats
        ];
    }
}
