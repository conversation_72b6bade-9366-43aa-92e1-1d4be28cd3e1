<?php
// Set layout variables
$body_class = 'error-page';
$container_class = 'container';

// Start output buffering for content
ob_start();
?>

<div class="row justify-content-center min-vh-100 align-items-center">
    <div class="col-md-8 col-lg-6 text-center">
        <div class="error-content">
            <div class="error-code mb-4">
                <h1 class="display-1 fw-bold text-danger">500</h1>
            </div>
            
            <div class="error-message mb-4">
                <h2 class="h3 mb-3">Internal Server Error</h2>
                <p class="text-muted mb-4">
                    <?php echo $this->escape($message ?? 'Something went wrong on our end. Please try again later.'); ?>
                </p>
            </div>
            
            <div class="error-actions">
                <a href="<?php echo $this->url(); ?>" class="btn btn-primary me-3">
                    <i class="fas fa-home me-2"></i>
                    Go Home
                </a>
                <button onclick="location.reload()" class="btn btn-outline-secondary">
                    <i class="fas fa-redo me-2"></i>
                    Try Again
                </button>
            </div>
            
            <div class="error-help mt-5">
                <p class="text-muted small">
                    If this problem persists, please contact support with the error details.
                </p>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.error-code h1 {
    font-size: 8rem;
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    color: white;
}

.error-message h2 {
    color: white;
}

.btn-primary {
    background: rgba(255,255,255,0.2);
    border: 2px solid rgba(255,255,255,0.3);
    color: white;
    backdrop-filter: blur(10px);
}

.btn-primary:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.5);
    color: white;
}

.btn-outline-secondary {
    border: 2px solid rgba(255,255,255,0.3);
    color: white;
    background: transparent;
}

.btn-outline-secondary:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.5);
    color: white;
}

@media (max-width: 768px) {
    .error-code h1 {
        font-size: 6rem;
    }
}
</style>

<?php
$content = ob_get_clean();

// Include the layout
include PM_THEMES_PATH . '/default/templates/layout.php';
?>
