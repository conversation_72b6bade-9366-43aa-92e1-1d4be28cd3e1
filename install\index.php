<?php
/**
 * ProjectManager Installation Wizard
 * 
 * WordPress-style installation process
 * 
 * @package ProjectManager\Install
 */

declare(strict_types=1);

// Prevent direct access if already installed
if (file_exists(__DIR__ . '/../config.php')) {
    header('HTTP/1.1 403 Forbidden');
    exit('Installation is not allowed when the application is already configured.');
}

// Define constants
define('PM_INSTALLING', true);
define('PM_ROOT', dirname(__DIR__));
define('PM_INSTALL_PATH', __DIR__);

// Start session for installation process
session_start();

// Get current step
$step = $_GET['step'] ?? 'welcome';
$allowedSteps = ['welcome', 'requirements', 'database', 'admin', 'complete'];

if (!in_array($step, $allowedSteps)) {
    $step = 'welcome';
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $step = handleFormSubmission($step);
}

// Include the installer class
require_once 'Installer.php';
$installer = new Installer();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProjectManager Installation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .installer {
            background: white;
            border-radius: 10px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 600px;
            overflow: hidden;
        }
        
        .installer-header {
            background: #2c3e50;
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .installer-header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .installer-header p {
            opacity: 0.8;
            font-size: 1.1rem;
        }
        
        .installer-body {
            padding: 40px;
        }
        
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 40px;
            padding: 0 20px;
        }
        
        .step {
            flex: 1;
            text-align: center;
            position: relative;
        }
        
        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 15px;
            right: -50%;
            width: 100%;
            height: 2px;
            background: #e0e0e0;
            z-index: 1;
        }
        
        .step.active:not(:last-child)::after,
        .step.completed:not(:last-child)::after {
            background: #4CAF50;
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e0e0e0;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            font-weight: bold;
            position: relative;
            z-index: 2;
        }
        
        .step.active .step-number {
            background: #2196F3;
            color: white;
        }
        
        .step.completed .step-number {
            background: #4CAF50;
            color: white;
        }
        
        .step-title {
            font-size: 0.9rem;
            color: #666;
        }
        
        .step.active .step-title {
            color: #2196F3;
            font-weight: bold;
        }
        
        .step.completed .step-title {
            color: #4CAF50;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #2196F3;
        }
        
        .form-group small {
            display: block;
            margin-top: 5px;
            color: #666;
            font-size: 0.9rem;
        }
        
        .btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #1976D2;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .requirements-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .requirements-table th,
        .requirements-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .requirements-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .status-ok {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .status-warning {
            color: #ffc107;
            font-weight: bold;
        }
        
        .installer-footer {
            background: #f8f9fa;
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e0e0e0;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 30px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #2196F3);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="installer">
        <div class="installer-header">
            <h1>ProjectManager</h1>
            <p>Welcome to the installation wizard</p>
        </div>
        
        <div class="installer-body">
            <?php renderProgressBar($step); ?>
            <?php renderStepIndicator($step); ?>
            <?php renderStep($step, $installer); ?>
        </div>
        
        <div class="installer-footer">
            <?php renderFooter($step); ?>
        </div>
    </div>
</body>
</html>

<?php

function handleFormSubmission($currentStep) {
    switch ($currentStep) {
        case 'requirements':
            return 'database';
            
        case 'database':
            if (testDatabaseConnection()) {
                return 'admin';
            }
            return 'database';
            
        case 'admin':
            if (createAdminUser()) {
                return 'complete';
            }
            return 'admin';
            
        default:
            return $currentStep;
    }
}

function testDatabaseConnection() {
    // Database connection testing logic
    return true; // Placeholder
}

function createAdminUser() {
    // Admin user creation logic
    return true; // Placeholder
}

function renderProgressBar($step) {
    $steps = ['welcome', 'requirements', 'database', 'admin', 'complete'];
    $currentIndex = array_search($step, $steps);
    $progress = (($currentIndex + 1) / count($steps)) * 100;
    
    echo "<div class='progress-bar'>";
    echo "<div class='progress-fill' style='width: {$progress}%'></div>";
    echo "</div>";
}

function renderStepIndicator($currentStep) {
    $steps = [
        'welcome' => 'Welcome',
        'requirements' => 'Requirements',
        'database' => 'Database',
        'admin' => 'Admin User',
        'complete' => 'Complete'
    ];
    
    echo "<div class='step-indicator'>";
    
    $stepIndex = 0;
    $currentIndex = array_search($currentStep, array_keys($steps));
    
    foreach ($steps as $stepKey => $stepTitle) {
        $stepIndex++;
        $class = '';
        
        if ($stepIndex - 1 < $currentIndex) {
            $class = 'completed';
        } elseif ($stepKey === $currentStep) {
            $class = 'active';
        }
        
        echo "<div class='step {$class}'>";
        echo "<div class='step-number'>{$stepIndex}</div>";
        echo "<div class='step-title'>{$stepTitle}</div>";
        echo "</div>";
    }
    
    echo "</div>";
}

function renderStep($step, $installer) {
    $stepFile = PM_INSTALL_PATH . "/steps/{$step}.php";
    
    if (file_exists($stepFile)) {
        include $stepFile;
    } else {
        echo "<div class='alert alert-danger'>Step file not found: {$step}</div>";
    }
}

function renderFooter($step) {
    $prevStep = getPreviousStep($step);
    $nextStep = getNextStep($step);
    
    echo "<div>";
    if ($prevStep && $step !== 'welcome') {
        echo "<a href='?step={$prevStep}' class='btn btn-secondary'>Previous</a>";
    }
    echo "</div>";
    
    echo "<div>";
    if ($nextStep && $step !== 'complete') {
        if ($step === 'welcome') {
            echo "<a href='?step={$nextStep}' class='btn btn-primary'>Get Started</a>";
        } else {
            echo "<button type='submit' form='install-form' class='btn btn-primary'>Continue</button>";
        }
    }
    echo "</div>";
}

function getPreviousStep($currentStep) {
    $steps = ['welcome', 'requirements', 'database', 'admin', 'complete'];
    $currentIndex = array_search($currentStep, $steps);
    
    return $currentIndex > 0 ? $steps[$currentIndex - 1] : null;
}

function getNextStep($currentStep) {
    $steps = ['welcome', 'requirements', 'database', 'admin', 'complete'];
    $currentIndex = array_search($currentStep, $steps);
    
    return $currentIndex < count($steps) - 1 ? $steps[$currentIndex + 1] : null;
}

?>
