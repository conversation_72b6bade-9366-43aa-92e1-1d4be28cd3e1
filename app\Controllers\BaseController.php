<?php
/**
 * Base Controller Class
 * 
 * Base class for all controllers
 * 
 * @package ProjectManager\Controllers
 */

declare(strict_types=1);

namespace App\Controllers;

use App\Core\Auth;
use App\Core\View;
use App\Core\Database;
use App\Core\Application;
use Exception;

/**
 * Base Controller Class
 */
abstract class BaseController
{
    /**
     * Auth instance
     * 
     * @var Auth
     */
    protected Auth $auth;
    
    /**
     * View instance
     * 
     * @var View
     */
    protected View $view;
    
    /**
     * Database instance
     * 
     * @var Database
     */
    protected Database $db;
    
    /**
     * Current user
     * 
     * @var array|null
     */
    protected ?array $currentUser = null;
    
    /**
     * Controller constructor
     * 
     * @param Auth $auth
     * @param View $view
     */
    public function __construct(Auth $auth, View $view)
    {
        $this->auth = $auth;
        $this->view = $view;
        $this->db = Application::getInstance()->getDatabase();
        $this->currentUser = $this->auth->getCurrentUser();
        
        // Add current user to global view data
        $this->view->addGlobal('current_user', $this->currentUser);
        $this->view->addGlobal('is_logged_in', $this->auth->isLoggedIn());
        
        // Add CSRF token to global view data
        $this->view->addGlobal('csrf_token', $this->auth->generateCsrfToken());
        
        // Call initialization hook
        $this->initialize();
    }
    
    /**
     * Initialize controller (override in child classes)
     */
    protected function initialize(): void
    {
        // Override in child classes
    }
    
    /**
     * Require authentication
     * 
     * @throws Exception
     */
    protected function requireAuth(): void
    {
        if (!$this->auth->isLoggedIn()) {
            $this->redirectToLogin();
        }
    }
    
    /**
     * Require specific permission
     * 
     * @param string $permission
     * @throws Exception
     */
    protected function requirePermission(string $permission): void
    {
        $this->requireAuth();
        
        if (!$this->auth->hasPermission($permission)) {
            $this->view->error(403, 'You do not have permission to access this resource.');
            exit;
        }
    }
    
    /**
     * Redirect to login page
     */
    protected function redirectToLogin(): void
    {
        $currentUrl = $_SERVER['REQUEST_URI'];
        $loginUrl = $this->view->url('login', ['redirect' => $currentUrl]);
        
        header("Location: {$loginUrl}");
        exit;
    }
    
    /**
     * Redirect to URL
     * 
     * @param string $url
     * @param int $code
     */
    protected function redirect(string $url, int $code = 302): void
    {
        header("Location: {$url}", true, $code);
        exit;
    }
    
    /**
     * Validate CSRF token
     * 
     * @return bool
     */
    protected function validateCsrfToken(): bool
    {
        $token = $_POST['csrf_token'] ?? $_GET['csrf_token'] ?? '';
        return $this->auth->verifyCsrfToken($token);
    }
    
    /**
     * Require CSRF token validation
     * 
     * @throws Exception
     */
    protected function requireCsrfToken(): void
    {
        if (!$this->validateCsrfToken()) {
            $this->view->error(403, 'Invalid security token. Please try again.');
            exit;
        }
    }
    
    /**
     * Get request input
     * 
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    protected function input(string $key, $default = null)
    {
        return $_POST[$key] ?? $_GET[$key] ?? $default;
    }
    
    /**
     * Get all request input
     * 
     * @return array
     */
    protected function allInput(): array
    {
        return array_merge($_GET, $_POST);
    }
    
    /**
     * Validate input data
     * 
     * @param array $data
     * @param array $rules
     * @return array Validation errors
     */
    protected function validate(array $data, array $rules): array
    {
        $errors = [];
        
        foreach ($rules as $field => $fieldRules) {
            $value = $data[$field] ?? null;
            $fieldRules = is_string($fieldRules) ? explode('|', $fieldRules) : $fieldRules;
            
            foreach ($fieldRules as $rule) {
                $error = $this->validateField($field, $value, $rule);
                if ($error) {
                    $errors[$field][] = $error;
                }
            }
        }
        
        return $errors;
    }
    
    /**
     * Validate a single field
     * 
     * @param string $field
     * @param mixed $value
     * @param string $rule
     * @return string|null
     */
    private function validateField(string $field, $value, string $rule): ?string
    {
        [$ruleName, $parameter] = explode(':', $rule . ':');
        
        switch ($ruleName) {
            case 'required':
                if (empty($value)) {
                    return "The {$field} field is required.";
                }
                break;
                
            case 'email':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    return "The {$field} must be a valid email address.";
                }
                break;
                
            case 'min':
                if (!empty($value) && strlen($value) < (int) $parameter) {
                    return "The {$field} must be at least {$parameter} characters.";
                }
                break;
                
            case 'max':
                if (!empty($value) && strlen($value) > (int) $parameter) {
                    return "The {$field} may not be greater than {$parameter} characters.";
                }
                break;
                
            case 'numeric':
                if (!empty($value) && !is_numeric($value)) {
                    return "The {$field} must be a number.";
                }
                break;
                
            case 'unique':
                if (!empty($value)) {
                    [$table, $column] = explode(',', $parameter . ',');
                    $column = $column ?: $field;
                    
                    $existing = $this->db->queryOne(
                        "SELECT id FROM {$this->db->getTable($table)} WHERE {$column} = ?",
                        [$value]
                    );
                    
                    if ($existing) {
                        return "The {$field} has already been taken.";
                    }
                }
                break;
        }
        
        return null;
    }
    
    /**
     * Flash message to session
     * 
     * @param string $type
     * @param string $message
     */
    protected function flash(string $type, string $message): void
    {
        $_SESSION['flash'][$type][] = $message;
    }
    
    /**
     * Get flash messages
     * 
     * @param string|null $type
     * @return array
     */
    protected function getFlash(?string $type = null): array
    {
        $flash = $_SESSION['flash'] ?? [];
        
        if ($type) {
            $messages = $flash[$type] ?? [];
            unset($_SESSION['flash'][$type]);
            return $messages;
        }
        
        unset($_SESSION['flash']);
        return $flash;
    }
    
    /**
     * Render JSON response
     * 
     * @param array $data
     * @param int $statusCode
     */
    protected function json(array $data, int $statusCode = 200): void
    {
        $this->view->json($data, $statusCode);
    }
    
    /**
     * Render success JSON response
     * 
     * @param array $data
     * @param string $message
     */
    protected function jsonSuccess(array $data = [], string $message = 'Success'): void
    {
        $this->json([
            'success' => true,
            'message' => $message,
            'data' => $data
        ]);
    }
    
    /**
     * Render error JSON response
     * 
     * @param string $message
     * @param array $errors
     * @param int $statusCode
     */
    protected function jsonError(string $message, array $errors = [], int $statusCode = 400): void
    {
        $this->json([
            'success' => false,
            'message' => $message,
            'errors' => $errors
        ], $statusCode);
    }
    
    /**
     * Get pagination data
     * 
     * @param int $total
     * @param int $page
     * @param int $perPage
     * @return array
     */
    protected function getPagination(int $total, int $page = 1, int $perPage = 20): array
    {
        $totalPages = ceil($total / $perPage);
        $offset = ($page - 1) * $perPage;
        
        return [
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'total_pages' => $totalPages,
            'offset' => $offset,
            'has_prev' => $page > 1,
            'has_next' => $page < $totalPages,
            'prev_page' => $page > 1 ? $page - 1 : null,
            'next_page' => $page < $totalPages ? $page + 1 : null
        ];
    }
}
