<?php
$adminData = $_SESSION['admin_data'] ?? null;
$dbConfig = $_SESSION['db_config'] ?? null;

// Clear session data
unset($_SESSION['admin_data'], $_SESSION['db_config']);
?>

<div class="complete-content">
    <div style="text-align: center; margin-bottom: 30px;">
        <div style="font-size: 4rem; color: #28a745; margin-bottom: 20px;">🎉</div>
        <h2 style="color: #28a745; margin-bottom: 10px;">Installation Complete!</h2>
        <p style="font-size: 1.2rem; color: #666;">ProjectManager has been successfully installed and configured.</p>
    </div>
    
    <div class="installation-summary" style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 30px 0;">
        <h3>📋 Installation Summary</h3>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 20px;">
            <div class="summary-item">
                <h4>✅ Database</h4>
                <p>Connected and configured successfully</p>
                <?php if ($dbConfig): ?>
                    <small>Host: <?php echo htmlspecialchars($dbConfig['host']); ?><br>
                    Database: <?php echo htmlspecialchars($dbConfig['database']); ?></small>
                <?php endif; ?>
            </div>
            
            <div class="summary-item">
                <h4>✅ Administrator</h4>
                <p>Admin account created successfully</p>
                <?php if ($adminData): ?>
                    <small>Name: <?php echo htmlspecialchars($adminData['full_name']); ?><br>
                    Email: <?php echo htmlspecialchars($adminData['email']); ?></small>
                <?php endif; ?>
            </div>
            
            <div class="summary-item">
                <h4>✅ Configuration</h4>
                <p>System configured and ready</p>
                <small>Config file: config.php<br>
                Tables: Created with sample data</small>
            </div>
            
            <div class="summary-item">
                <h4>✅ Security</h4>
                <p>Installation secured</p>
                <small>Install directory protected<br>
                Secure configuration generated</small>
            </div>
        </div>
    </div>
    
    <div class="next-steps" style="background: #e3f2fd; padding: 25px; border-radius: 10px; margin: 30px 0;">
        <h3>🚀 What's Next?</h3>
        
        <div style="margin: 20px 0;">
            <h4>1. Login to Your Dashboard</h4>
            <p>Use your administrator credentials to access the ProjectManager dashboard and start creating projects.</p>
            <a href="../" class="btn btn-success" style="margin-top: 10px;">Go to Dashboard</a>
        </div>
        
        <div style="margin: 20px 0;">
            <h4>2. Customize Your Installation</h4>
            <ul>
                <li>Update your profile and preferences</li>
                <li>Configure email notifications</li>
                <li>Set up your organization details</li>
                <li>Customize the appearance and branding</li>
            </ul>
        </div>
        
        <div style="margin: 20px 0;">
            <h4>3. Create Your First Project</h4>
            <ul>
                <li>Add team members and assign roles</li>
                <li>Create projects and organize tasks</li>
                <li>Set up project milestones and deadlines</li>
                <li>Start collaborating with your team</li>
            </ul>
        </div>
        
        <div style="margin: 20px 0;">
            <h4>4. Explore Advanced Features</h4>
            <ul>
                <li>Install plugins to extend functionality</li>
                <li>Customize themes for your brand</li>
                <li>Set up automated backups</li>
                <li>Configure integrations with other tools</li>
            </ul>
        </div>
    </div>
    
    <div class="security-recommendations" style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 30px 0;">
        <h3>🔒 Important Security Recommendations</h3>
        
        <div style="margin: 15px 0;">
            <h4>Immediate Actions:</h4>
            <ul>
                <li><strong>Delete or secure the install directory:</strong> Remove the <code>/install</code> directory or restrict access to it</li>
                <li><strong>Update file permissions:</strong> Ensure config.php is not publicly readable (644 permissions)</li>
                <li><strong>Enable HTTPS:</strong> Use SSL/TLS encryption for all connections</li>
                <li><strong>Regular backups:</strong> Set up automated database and file backups</li>
            </ul>
        </div>
        
        <div style="margin: 15px 0;">
            <h4>Ongoing Security:</h4>
            <ul>
                <li>Keep ProjectManager updated to the latest version</li>
                <li>Use strong passwords for all user accounts</li>
                <li>Enable two-factor authentication for administrators</li>
                <li>Monitor login attempts and user activity</li>
                <li>Regularly review user permissions and access</li>
            </ul>
        </div>
    </div>
    
    <div class="support-resources" style="background: #d1ecf1; padding: 20px; border-radius: 8px; margin: 30px 0;">
        <h3>📚 Resources & Support</h3>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
            <div>
                <h4>📖 Documentation</h4>
                <p>Complete user and administrator guides</p>
            </div>
            
            <div>
                <h4>🎥 Video Tutorials</h4>
                <p>Step-by-step video guides for common tasks</p>
            </div>
            
            <div>
                <h4>💬 Community Forum</h4>
                <p>Get help from other ProjectManager users</p>
            </div>
            
            <div>
                <h4>🐛 Bug Reports</h4>
                <p>Report issues and request new features</p>
            </div>
        </div>
    </div>
    
    <div class="final-message" style="text-align: center; margin: 40px 0; padding: 30px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px;">
        <h3>Welcome to ProjectManager!</h3>
        <p style="font-size: 1.1rem; margin: 15px 0;">
            Thank you for choosing ProjectManager for your project management needs. 
            We're excited to help you and your team stay organized and productive.
        </p>
        <p style="margin-top: 20px;">
            <a href="../" class="btn" style="background: white; color: #667eea; font-weight: bold; padding: 15px 30px; font-size: 1.1rem;">
                Start Managing Projects →
            </a>
        </p>
    </div>
</div>

<script>
// Disable browser back button on completion page
history.pushState(null, null, location.href);
window.onpopstate = function () {
    history.go(1);
};

// Auto-redirect after 30 seconds
setTimeout(function() {
    if (confirm('Would you like to go to the dashboard now?')) {
        window.location.href = '../';
    }
}, 30000);
</script>
