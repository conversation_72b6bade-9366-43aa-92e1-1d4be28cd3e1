<?php
/**
 * Main Application Class
 * 
 * Bootstrap and run the ProjectManager application
 * 
 * @package ProjectManager\Core
 */

declare(strict_types=1);

namespace App\Core;

use Exception;

/**
 * Application Bootstrap Class
 */
class Application
{
    /**
     * Application instance
     * 
     * @var Application|null
     */
    private static ?Application $instance = null;
    
    /**
     * Router instance
     * 
     * @var Router
     */
    private Router $router;
    
    /**
     * Database instance
     * 
     * @var Database
     */
    private Database $database;
    
    /**
     * Hook system instance
     * 
     * @var Hook
     */
    private Hook $hook;
    
    /**
     * Authentication instance
     * 
     * @var Auth
     */
    private Auth $auth;
    
    /**
     * View renderer instance
     * 
     * @var View
     */
    private View $view;
    
    /**
     * Cache instance
     * 
     * @var Cache
     */
    private Cache $cache;
    
    /**
     * Application constructor
     */
    public function __construct()
    {
        self::$instance = $this;
        
        // Set error reporting based on environment
        $this->setupErrorReporting();
        
        // Set timezone
        if (defined('PM_TIMEZONE')) {
            date_default_timezone_set(PM_TIMEZONE);
        }
        
        // Initialize core components
        $this->initializeComponents();
        
        // Load plugins and themes
        $this->loadPlugins();
        $this->loadTheme();
    }
    
    /**
     * Get application instance
     * 
     * @return Application
     */
    public static function getInstance(): Application
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        
        return self::$instance;
    }
    
    /**
     * Run the application
     */
    public function run(): void
    {
        try {
            // Start session
            $this->auth->startSession();
            
            // Apply security headers
            $this->applySecurityHeaders();
            
            // Handle the request
            $this->router->handleRequest();
            
        } catch (Exception $e) {
            $this->handleException($e);
        }
    }
    
    /**
     * Setup error reporting based on environment
     */
    private function setupErrorReporting(): void
    {
        if (defined('PM_DEBUG') && PM_DEBUG) {
            error_reporting(E_ALL);
            ini_set('display_errors', '1');
            ini_set('log_errors', '1');
        } else {
            error_reporting(E_ERROR | E_WARNING | E_PARSE);
            ini_set('display_errors', '0');
            ini_set('log_errors', '1');
        }
        
        // Set error log file
        if (defined('PM_STORAGE_PATH')) {
            ini_set('error_log', PM_STORAGE_PATH . '/logs/php_errors.log');
        }
    }
    
    /**
     * Initialize core components
     */
    private function initializeComponents(): void
    {
        // Initialize hook system first
        $this->hook = new Hook();
        
        // Initialize database
        $this->database = new Database();
        
        // Initialize cache
        $this->cache = new Cache();
        
        // Initialize authentication
        $this->auth = new Auth($this->database);
        
        // Initialize view renderer
        $this->view = new View();
        
        // Initialize router
        $this->router = new Router($this->auth, $this->view);
        
        // Fire application initialized hook
        $this->hook->doAction('pm_application_initialized', $this);
    }
    
    /**
     * Load active plugins
     */
    private function loadPlugins(): void
    {
        if (!defined('PM_ALLOW_PLUGINS') || !PM_ALLOW_PLUGINS) {
            return;
        }
        
        // Get active plugins from database or cache
        $activePlugins = $this->getActivePlugins();
        
        foreach ($activePlugins as $plugin) {
            $pluginFile = PM_PLUGINS_PATH . '/' . $plugin . '/' . $plugin . '.php';
            if (file_exists($pluginFile)) {
                require_once $pluginFile;
            }
        }
        
        // Fire plugins loaded hook
        $this->hook->doAction('pm_plugins_loaded');
    }
    
    /**
     * Load active theme
     */
    private function loadTheme(): void
    {
        $activeTheme = $this->getActiveTheme();
        $themeFile = PM_THEMES_PATH . '/' . $activeTheme . '/functions.php';
        
        if (file_exists($themeFile)) {
            require_once $themeFile;
        }
        
        // Fire theme loaded hook
        $this->hook->doAction('pm_theme_loaded', $activeTheme);
    }
    
    /**
     * Apply security headers
     */
    private function applySecurityHeaders(): void
    {
        // Only apply if not already sent
        if (!headers_sent()) {
            header('X-Content-Type-Options: nosniff');
            header('X-Frame-Options: DENY');
            header('X-XSS-Protection: 1; mode=block');
            header('Referrer-Policy: strict-origin-when-cross-origin');
            
            // HSTS for HTTPS
            if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
                header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
            }
            
            // Content Security Policy
            if (defined('PM_CSP_ENABLED') && PM_CSP_ENABLED) {
                $csp = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self';";
                
                if (defined('PM_CSP_REPORT_ONLY') && PM_CSP_REPORT_ONLY) {
                    header('Content-Security-Policy-Report-Only: ' . $csp);
                } else {
                    header('Content-Security-Policy: ' . $csp);
                }
            }
        }
    }
    
    /**
     * Handle application exceptions
     * 
     * @param Exception $e
     */
    private function handleException(Exception $e): void
    {
        // Log the error
        error_log('ProjectManager Exception: ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());
        
        // Show appropriate error page
        if (defined('PM_DEBUG') && PM_DEBUG) {
            $this->view->render('errors/debug', [
                'exception' => $e
            ]);
        } else {
            $this->view->render('errors/500', [
                'message' => 'An internal error occurred. Please try again later.'
            ]);
        }
    }
    
    /**
     * Get active plugins
     * 
     * @return array
     */
    private function getActivePlugins(): array
    {
        // TODO: Implement plugin management
        return [];
    }
    
    /**
     * Get active theme
     * 
     * @return string
     */
    private function getActiveTheme(): string
    {
        return defined('PM_DEFAULT_THEME') ? PM_DEFAULT_THEME : 'default';
    }
    
    /**
     * Get component instances
     */
    public function getRouter(): Router { return $this->router; }
    public function getDatabase(): Database { return $this->database; }
    public function getHook(): Hook { return $this->hook; }
    public function getAuth(): Auth { return $this->auth; }
    public function getView(): View { return $this->view; }
    public function getCache(): Cache { return $this->cache; }
}
