<?php
/**
 * Database Abstraction Layer
 * 
 * Provides secure database operations with prepared statements
 * 
 * @package ProjectManager\Core
 */

declare(strict_types=1);

namespace App\Core;

use PDO;
use PDOException;
use PDOStatement;
use Exception;

/**
 * Database Class
 */
class Database
{
    /**
     * PDO connection instance
     * 
     * @var PDO|null
     */
    private ?PDO $connection = null;
    
    /**
     * Table prefix
     * 
     * @var string
     */
    private string $tablePrefix;
    
    /**
     * Query log for debugging
     * 
     * @var array
     */
    private array $queryLog = [];
    
    /**
     * Database constructor
     */
    public function __construct()
    {
        $this->tablePrefix = $GLOBALS['table_prefix'] ?? 'pm_';
        $this->connect();
    }
    
    /**
     * Establish database connection
     * 
     * @throws Exception
     */
    private function connect(): void
    {
        try {
            $dsn = sprintf(
                'mysql:host=%s;dbname=%s;charset=%s',
                DB_HOST,
                DB_NAME,
                DB_CHARSET
            );
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET . " COLLATE " . DB_COLLATE,
                PDO::ATTR_PERSISTENT => true
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASSWORD, $options);
            
        } catch (PDOException $e) {
            throw new Exception('Database connection failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Get PDO connection
     * 
     * @return PDO
     */
    public function getConnection(): PDO
    {
        if ($this->connection === null) {
            $this->connect();
        }
        
        return $this->connection;
    }
    
    /**
     * Execute a prepared statement
     * 
     * @param string $sql
     * @param array $params
     * @return PDOStatement
     * @throws Exception
     */
    public function prepare(string $sql, array $params = []): PDOStatement
    {
        try {
            $statement = $this->getConnection()->prepare($sql);
            
            // Log query for debugging
            if (defined('PM_DEBUG') && PM_DEBUG) {
                $this->queryLog[] = [
                    'sql' => $sql,
                    'params' => $params,
                    'time' => microtime(true)
                ];
            }
            
            $statement->execute($params);
            
            return $statement;
            
        } catch (PDOException $e) {
            throw new Exception('Database query failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Execute a query and return all results
     * 
     * @param string $sql
     * @param array $params
     * @return array
     */
    public function query(string $sql, array $params = []): array
    {
        $statement = $this->prepare($sql, $params);
        return $statement->fetchAll();
    }
    
    /**
     * Execute a query and return first result
     * 
     * @param string $sql
     * @param array $params
     * @return array|null
     */
    public function queryOne(string $sql, array $params = []): ?array
    {
        $statement = $this->prepare($sql, $params);
        $result = $statement->fetch();
        
        return $result ?: null;
    }
    
    /**
     * Execute an insert query
     * 
     * @param string $table
     * @param array $data
     * @return int Last insert ID
     */
    public function insert(string $table, array $data): int
    {
        $table = $this->tablePrefix . $table;
        $columns = array_keys($data);
        $placeholders = ':' . implode(', :', $columns);
        
        $sql = "INSERT INTO `{$table}` (`" . implode('`, `', $columns) . "`) VALUES ({$placeholders})";
        
        $this->prepare($sql, $data);
        
        return (int) $this->getConnection()->lastInsertId();
    }
    
    /**
     * Execute an update query
     * 
     * @param string $table
     * @param array $data
     * @param array $where
     * @return int Number of affected rows
     */
    public function update(string $table, array $data, array $where): int
    {
        $table = $this->tablePrefix . $table;
        
        $setClause = [];
        foreach (array_keys($data) as $column) {
            $setClause[] = "`{$column}` = :{$column}";
        }
        
        $whereClause = [];
        foreach (array_keys($where) as $column) {
            $whereClause[] = "`{$column}` = :where_{$column}";
        }
        
        $sql = "UPDATE `{$table}` SET " . implode(', ', $setClause) . " WHERE " . implode(' AND ', $whereClause);
        
        // Merge data and where parameters
        $params = $data;
        foreach ($where as $key => $value) {
            $params["where_{$key}"] = $value;
        }
        
        $statement = $this->prepare($sql, $params);
        
        return $statement->rowCount();
    }
    
    /**
     * Execute a delete query
     * 
     * @param string $table
     * @param array $where
     * @return int Number of affected rows
     */
    public function delete(string $table, array $where): int
    {
        $table = $this->tablePrefix . $table;
        
        $whereClause = [];
        foreach (array_keys($where) as $column) {
            $whereClause[] = "`{$column}` = :{$column}";
        }
        
        $sql = "DELETE FROM `{$table}` WHERE " . implode(' AND ', $whereClause);
        
        $statement = $this->prepare($sql, $where);
        
        return $statement->rowCount();
    }
    
    /**
     * Get table name with prefix
     * 
     * @param string $table
     * @return string
     */
    public function getTable(string $table): string
    {
        return $this->tablePrefix . $table;
    }
    
    /**
     * Begin transaction
     */
    public function beginTransaction(): void
    {
        $this->getConnection()->beginTransaction();
    }
    
    /**
     * Commit transaction
     */
    public function commit(): void
    {
        $this->getConnection()->commit();
    }
    
    /**
     * Rollback transaction
     */
    public function rollback(): void
    {
        $this->getConnection()->rollBack();
    }
    
    /**
     * Check if table exists
     * 
     * @param string $table
     * @return bool
     */
    public function tableExists(string $table): bool
    {
        $table = $this->tablePrefix . $table;
        
        $sql = "SHOW TABLES LIKE :table";
        $result = $this->queryOne($sql, ['table' => $table]);
        
        return $result !== null;
    }
    
    /**
     * Get query log
     * 
     * @return array
     */
    public function getQueryLog(): array
    {
        return $this->queryLog;
    }
    
    /**
     * Clear query log
     */
    public function clearQueryLog(): void
    {
        $this->queryLog = [];
    }
}
