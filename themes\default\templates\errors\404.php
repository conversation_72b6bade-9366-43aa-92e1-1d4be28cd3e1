<?php
// Set layout variables
$body_class = 'error-page';
$container_class = 'container';

// Start output buffering for content
ob_start();
?>

<div class="row justify-content-center min-vh-100 align-items-center">
    <div class="col-md-8 col-lg-6 text-center">
        <div class="error-content">
            <div class="error-code mb-4">
                <h1 class="display-1 fw-bold text-primary">404</h1>
            </div>
            
            <div class="error-message mb-4">
                <h2 class="h3 mb-3">Page Not Found</h2>
                <p class="text-muted mb-4">
                    Sorry, the page you are looking for doesn't exist or has been moved.
                </p>
            </div>
            
            <div class="error-actions">
                <a href="<?php echo $this->url(); ?>" class="btn btn-primary me-3">
                    <i class="fas fa-home me-2"></i>
                    Go Home
                </a>
                <button onclick="history.back()" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Go Back
                </button>
            </div>
            
            <div class="error-help mt-5">
                <p class="text-muted small">
                    If you believe this is an error, please contact support.
                </p>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.error-code h1 {
    font-size: 8rem;
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.error-message h2 {
    color: white;
}

.btn-primary {
    background: rgba(255,255,255,0.2);
    border: 2px solid rgba(255,255,255,0.3);
    color: white;
    backdrop-filter: blur(10px);
}

.btn-primary:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.5);
    color: white;
}

.btn-outline-secondary {
    border: 2px solid rgba(255,255,255,0.3);
    color: white;
    background: transparent;
}

.btn-outline-secondary:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.5);
    color: white;
}

@media (max-width: 768px) {
    .error-code h1 {
        font-size: 6rem;
    }
}
</style>

<?php
$content = ob_get_clean();

// Include the layout
include PM_THEMES_PATH . '/default/templates/layout.php';
?>
