<?php
/**
 * User Controller
 * 
 * Handle user authentication and profile management
 * 
 * @package ProjectManager\Controllers
 */

declare(strict_types=1);

namespace App\Controllers;

/**
 * User Controller Class
 */
class UserController extends BaseController
{
    /**
     * Show login form
     * 
     * @param array $params
     */
    public function showLogin(array $params = []): void
    {
        // Redirect if already logged in
        if ($this->auth->isLoggedIn()) {
            $this->redirect($this->view->url('dashboard'));
            return;
        }
        
        $this->view->render('auth/login', [
            'page_title' => 'Login',
            'redirect' => $_GET['redirect'] ?? '',
            'flash_messages' => $this->getFlash()
        ]);
    }
    
    /**
     * Handle login form submission
     * 
     * @param array $params
     */
    public function login(array $params = []): void
    {
        // Redirect if already logged in
        if ($this->auth->isLoggedIn()) {
            $this->redirect($this->view->url('dashboard'));
            return;
        }
        
        // Validate CSRF token
        $this->requireCsrfToken();
        
        $email = trim($this->input('email', ''));
        $password = $this->input('password', '');
        $redirect = $this->input('redirect', '');
        
        // Validate input
        $errors = $this->validate([
            'email' => $email,
            'password' => $password
        ], [
            'email' => 'required|email',
            'password' => 'required'
        ]);
        
        if (empty($errors)) {
            // Attempt login
            if ($this->auth->login($email, $password)) {
                $this->flash('success', 'Welcome back! You have been logged in successfully.');
                
                // Redirect to intended page or dashboard
                $redirectUrl = $redirect ?: $this->view->url('dashboard');
                $this->redirect($redirectUrl);
                return;
            } else {
                $this->flash('error', 'Invalid email or password. Please try again.');
            }
        } else {
            foreach ($errors as $field => $fieldErrors) {
                foreach ($fieldErrors as $error) {
                    $this->flash('error', $error);
                }
            }
        }
        
        // Redirect back to login form
        $this->redirect($this->view->url('login', $redirect ? ['redirect' => $redirect] : []));
    }
    
    /**
     * Show registration form
     * 
     * @param array $params
     */
    public function showRegister(array $params = []): void
    {
        // Check if registration is enabled
        if (!$this->isRegistrationEnabled()) {
            $this->view->error(403, 'Registration is currently disabled.');
            return;
        }
        
        // Redirect if already logged in
        if ($this->auth->isLoggedIn()) {
            $this->redirect($this->view->url('dashboard'));
            return;
        }
        
        $this->view->render('auth/register', [
            'page_title' => 'Register',
            'flash_messages' => $this->getFlash()
        ]);
    }
    
    /**
     * Handle registration form submission
     * 
     * @param array $params
     */
    public function register(array $params = []): void
    {
        // Check if registration is enabled
        if (!$this->isRegistrationEnabled()) {
            $this->view->error(403, 'Registration is currently disabled.');
            return;
        }
        
        // Redirect if already logged in
        if ($this->auth->isLoggedIn()) {
            $this->redirect($this->view->url('dashboard'));
            return;
        }
        
        // Validate CSRF token
        $this->requireCsrfToken();
        
        $userData = [
            'full_name' => trim($this->input('full_name', '')),
            'email' => trim($this->input('email', '')),
            'password' => $this->input('password', ''),
            'password_confirm' => $this->input('password_confirm', '')
        ];
        
        // Validate input
        $errors = $this->validate($userData, [
            'full_name' => 'required|min:2|max:190',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|min:8'
        ]);
        
        // Check password confirmation
        if ($userData['password'] !== $userData['password_confirm']) {
            $errors['password_confirm'][] = 'Passwords do not match.';
        }
        
        if (empty($errors)) {
            try {
                // Create user account
                $userId = $this->createUser($userData);
                
                if ($userId) {
                    $this->flash('success', 'Your account has been created successfully. You can now log in.');
                    $this->redirect($this->view->url('login'));
                    return;
                } else {
                    $this->flash('error', 'Failed to create account. Please try again.');
                }
            } catch (\Exception $e) {
                $this->flash('error', 'An error occurred while creating your account. Please try again.');
                error_log('Registration error: ' . $e->getMessage());
            }
        } else {
            foreach ($errors as $field => $fieldErrors) {
                foreach ($fieldErrors as $error) {
                    $this->flash('error', $error);
                }
            }
        }
        
        // Redirect back to registration form
        $this->redirect($this->view->url('register'));
    }
    
    /**
     * Handle logout
     * 
     * @param array $params
     */
    public function logout(array $params = []): void
    {
        $this->auth->logout();
        $this->flash('success', 'You have been logged out successfully.');
        $this->redirect($this->view->url('login'));
    }
    
    /**
     * Show user profile
     * 
     * @param array $params
     */
    public function profile(array $params = []): void
    {
        $this->requireAuth();
        
        $this->view->render('user/profile', [
            'page_title' => 'My Profile',
            'user' => $this->currentUser,
            'flash_messages' => $this->getFlash()
        ]);
    }
    
    /**
     * Update user profile
     * 
     * @param array $params
     */
    public function updateProfile(array $params = []): void
    {
        $this->requireAuth();
        $this->requireCsrfToken();
        
        $userData = [
            'full_name' => trim($this->input('full_name', '')),
            'email' => trim($this->input('email', '')),
            'timezone' => $this->input('timezone', 'UTC'),
            'locale' => $this->input('locale', 'en_US')
        ];
        
        // Validate input
        $errors = $this->validate($userData, [
            'full_name' => 'required|min:2|max:190',
            'email' => 'required|email'
        ]);
        
        // Check if email is unique (excluding current user)
        if (!empty($userData['email'])) {
            $existingUser = $this->db->queryOne(
                "SELECT id FROM {$this->db->getTable('users')} WHERE email = ? AND id != ?",
                [$userData['email'], $this->currentUser['id']]
            );
            
            if ($existingUser) {
                $errors['email'][] = 'This email address is already in use.';
            }
        }
        
        if (empty($errors)) {
            // Update user profile
            $updated = $this->db->update('users', [
                'full_name' => $userData['full_name'],
                'email' => $userData['email'],
                'timezone' => $userData['timezone'],
                'locale' => $userData['locale']
            ], ['id' => $this->currentUser['id']]);
            
            if ($updated) {
                $this->flash('success', 'Your profile has been updated successfully.');
            } else {
                $this->flash('error', 'Failed to update profile. Please try again.');
            }
        } else {
            foreach ($errors as $field => $fieldErrors) {
                foreach ($fieldErrors as $error) {
                    $this->flash('error', $error);
                }
            }
        }
        
        $this->redirect($this->view->url('profile'));
    }
    
    /**
     * Check if registration is enabled
     * 
     * @return bool
     */
    private function isRegistrationEnabled(): bool
    {
        // TODO: Get from settings table
        return defined('PM_REGISTRATION_ENABLED') ? PM_REGISTRATION_ENABLED : true;
    }
    
    /**
     * Create a new user account
     * 
     * @param array $userData
     * @return int|null User ID
     */
    private function createUser(array $userData): ?int
    {
        $hashedPassword = $this->auth->hashPassword($userData['password']);
        
        $userId = $this->db->insert('users', [
            'full_name' => $userData['full_name'],
            'email' => $userData['email'],
            'password' => $hashedPassword,
            'status' => 'active', // TODO: Set to 'pending' if email verification is required
            'email_verified_at' => date('Y-m-d H:i:s') // TODO: Set to null if email verification is required
        ]);
        
        if ($userId) {
            // Assign default role
            $this->assignDefaultRole($userId);
        }
        
        return $userId;
    }
    
    /**
     * Assign default role to user
     * 
     * @param int $userId
     */
    private function assignDefaultRole(int $userId): void
    {
        // Get default role
        $defaultRole = $this->db->queryOne(
            "SELECT id FROM {$this->db->getTable('user_roles')} WHERE name = 'member'"
        );
        
        if ($defaultRole) {
            $this->db->insert('user_role_assignments', [
                'user_id' => $userId,
                'role_id' => $defaultRole['id']
            ]);
        }
    }
}
