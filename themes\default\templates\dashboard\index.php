<?php
// Set layout variables
$body_class = 'dashboard-page';

// Set breadcrumbs
$breadcrumbs = [
    ['title' => 'Dashboard', 'url' => $this->url('dashboard')]
];

// Start output buffering for content
ob_start();
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1">Dashboard</h1>
                <p class="text-muted mb-0">Welcome back, <?php echo $this->escape($current_user['full_name']); ?>!</p>
            </div>
            <div>
                <a href="<?php echo $this->url('projects/create'); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    New Project
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-1">Projects</h5>
                        <h2 class="mb-0"><?php echo $dashboard_data['stats']['total_projects']; ?></h2>
                    </div>
                    <div class="fs-1 opacity-75">
                        <i class="fas fa-folder"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-1">Total Tasks</h5>
                        <h2 class="mb-0"><?php echo $dashboard_data['stats']['total_tasks']; ?></h2>
                    </div>
                    <div class="fs-1 opacity-75">
                        <i class="fas fa-tasks"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-1">Completed</h5>
                        <h2 class="mb-0"><?php echo $dashboard_data['stats']['completed_tasks']; ?></h2>
                    </div>
                    <div class="fs-1 opacity-75">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card <?php echo $dashboard_data['stats']['overdue_tasks'] > 0 ? 'bg-danger' : 'bg-secondary'; ?> text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-1">Overdue</h5>
                        <h2 class="mb-0"><?php echo $dashboard_data['stats']['overdue_tasks']; ?></h2>
                    </div>
                    <div class="fs-1 opacity-75">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Progress Overview -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Overall Progress</h5>
                <div class="progress mb-2" style="height: 20px;">
                    <div class="progress-bar bg-success" 
                         role="progressbar" 
                         style="width: <?php echo $dashboard_data['stats']['completion_percentage']; ?>%"
                         aria-valuenow="<?php echo $dashboard_data['stats']['completion_percentage']; ?>" 
                         aria-valuemin="0" 
                         aria-valuemax="100">
                        <?php echo $dashboard_data['stats']['completion_percentage']; ?>%
                    </div>
                </div>
                <small class="text-muted">
                    <?php echo $dashboard_data['stats']['completed_tasks']; ?> of <?php echo $dashboard_data['stats']['total_tasks']; ?> tasks completed
                </small>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Projects -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Recent Projects</h5>
                <a href="<?php echo $this->url('projects'); ?>" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <?php if (!empty($dashboard_data['projects'])): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($dashboard_data['projects'] as $project): ?>
                            <div class="list-group-item px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <a href="<?php echo $this->url('projects/' . $project['id']); ?>" 
                                               class="text-decoration-none">
                                                <?php echo $this->escape($project['name']); ?>
                                            </a>
                                        </h6>
                                        <p class="mb-1 text-muted small">
                                            <?php echo $this->escape(substr($project['description'] ?? '', 0, 100)); ?>
                                            <?php if (strlen($project['description'] ?? '') > 100): ?>...<?php endif; ?>
                                        </p>
                                        <small class="text-muted">
                                            <?php echo $project['total_tasks']; ?> tasks 
                                            (<?php echo $project['completed_tasks']; ?> completed)
                                        </small>
                                    </div>
                                    <div class="ms-3">
                                        <span class="badge bg-<?php echo $this->getStatusColor($project['status']); ?>">
                                            <?php echo ucfirst($project['status']); ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-folder-open text-muted mb-3" style="font-size: 3rem;"></i>
                        <h6 class="text-muted">No projects yet</h6>
                        <p class="text-muted small">Create your first project to get started</p>
                        <a href="<?php echo $this->url('projects/create'); ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>
                            Create Project
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Pending Tasks -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Pending Tasks</h5>
                <a href="<?php echo $this->url('tasks'); ?>" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <?php if (!empty($dashboard_data['pending_tasks'])): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($dashboard_data['pending_tasks'] as $task): ?>
                            <div class="list-group-item px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <a href="<?php echo $this->url('tasks/' . $task['id']); ?>" 
                                               class="text-decoration-none">
                                                <?php echo $this->escape($task['title']); ?>
                                            </a>
                                        </h6>
                                        <p class="mb-1 text-muted small">
                                            Project: <?php echo $this->escape($task['project_name']); ?>
                                        </p>
                                        <?php if ($task['due_date']): ?>
                                            <small class="text-<?php echo strtotime($task['due_date']) < time() ? 'danger' : 'muted'; ?>">
                                                <i class="fas fa-calendar me-1"></i>
                                                Due: <?php echo $this->formatDate($task['due_date'], 'M j, Y'); ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                    <div class="ms-3">
                                        <span class="badge bg-<?php echo $this->getPriorityColor($task['priority']); ?>">
                                            <?php echo ucfirst($task['priority']); ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle text-success mb-3" style="font-size: 3rem;"></i>
                        <h6 class="text-muted">All caught up!</h6>
                        <p class="text-muted small">No pending tasks at the moment</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Overdue Tasks Alert -->
<?php if (!empty($dashboard_data['overdue_tasks'])): ?>
<div class="row">
    <div class="col-12">
        <div class="alert alert-danger">
            <h5 class="alert-heading">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Overdue Tasks
            </h5>
            <p class="mb-2">You have <?php echo count($dashboard_data['overdue_tasks']); ?> overdue task(s) that need attention:</p>
            <ul class="mb-0">
                <?php foreach (array_slice($dashboard_data['overdue_tasks'], 0, 3) as $task): ?>
                    <li>
                        <a href="<?php echo $this->url('tasks/' . $task['id']); ?>" class="text-decoration-none">
                            <?php echo $this->escape($task['title']); ?>
                        </a>
                        <small class="text-muted">
                            (Due: <?php echo $this->formatDate($task['due_date'], 'M j, Y'); ?>)
                        </small>
                    </li>
                <?php endforeach; ?>
                <?php if (count($dashboard_data['overdue_tasks']) > 3): ?>
                    <li>
                        <a href="<?php echo $this->url('tasks', ['filter' => 'overdue']); ?>">
                            And <?php echo count($dashboard_data['overdue_tasks']) - 3; ?> more...
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</div>
<?php endif; ?>

<?php
$content = ob_get_clean();

// Include the layout
include PM_THEMES_PATH . '/default/templates/layout.php';
?>
