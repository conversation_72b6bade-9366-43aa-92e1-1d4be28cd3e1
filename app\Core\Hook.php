<?php
/**
 * Hook System Class
 * 
 * WordPress-style action and filter hooks
 * 
 * @package ProjectManager\Core
 */

declare(strict_types=1);

namespace App\Core;

/**
 * Hook System Class
 */
class Hook
{
    /**
     * Registered actions
     * 
     * @var array
     */
    private static array $actions = [];
    
    /**
     * Registered filters
     * 
     * @var array
     */
    private static array $filters = [];
    
    /**
     * Current filter being executed
     * 
     * @var string|null
     */
    private static ?string $currentFilter = null;
    
    /**
     * Add an action hook
     * 
     * @param string $tag
     * @param callable $callback
     * @param int $priority
     * @param int $acceptedArgs
     */
    public function addAction(string $tag, callable $callback, int $priority = 10, int $acceptedArgs = 1): void
    {
        self::$actions[$tag][$priority][] = [
            'callback' => $callback,
            'accepted_args' => $acceptedArgs
        ];
        
        // Sort by priority
        if (isset(self::$actions[$tag])) {
            ksort(self::$actions[$tag]);
        }
    }
    
    /**
     * Remove an action hook
     * 
     * @param string $tag
     * @param callable $callback
     * @param int $priority
     * @return bool
     */
    public function removeAction(string $tag, callable $callback, int $priority = 10): bool
    {
        if (!isset(self::$actions[$tag][$priority])) {
            return false;
        }
        
        foreach (self::$actions[$tag][$priority] as $index => $action) {
            if ($action['callback'] === $callback) {
                unset(self::$actions[$tag][$priority][$index]);
                
                // Clean up empty priority levels
                if (empty(self::$actions[$tag][$priority])) {
                    unset(self::$actions[$tag][$priority]);
                }
                
                // Clean up empty tags
                if (empty(self::$actions[$tag])) {
                    unset(self::$actions[$tag]);
                }
                
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Execute action hooks
     * 
     * @param string $tag
     * @param mixed ...$args
     */
    public function doAction(string $tag, ...$args): void
    {
        if (!isset(self::$actions[$tag])) {
            return;
        }
        
        foreach (self::$actions[$tag] as $priority => $actions) {
            foreach ($actions as $action) {
                $callback = $action['callback'];
                $acceptedArgs = $action['accepted_args'];
                
                // Limit arguments to accepted count
                $callArgs = array_slice($args, 0, $acceptedArgs);
                
                call_user_func_array($callback, $callArgs);
            }
        }
    }
    
    /**
     * Add a filter hook
     * 
     * @param string $tag
     * @param callable $callback
     * @param int $priority
     * @param int $acceptedArgs
     */
    public function addFilter(string $tag, callable $callback, int $priority = 10, int $acceptedArgs = 1): void
    {
        self::$filters[$tag][$priority][] = [
            'callback' => $callback,
            'accepted_args' => $acceptedArgs
        ];
        
        // Sort by priority
        if (isset(self::$filters[$tag])) {
            ksort(self::$filters[$tag]);
        }
    }
    
    /**
     * Remove a filter hook
     * 
     * @param string $tag
     * @param callable $callback
     * @param int $priority
     * @return bool
     */
    public function removeFilter(string $tag, callable $callback, int $priority = 10): bool
    {
        if (!isset(self::$filters[$tag][$priority])) {
            return false;
        }
        
        foreach (self::$filters[$tag][$priority] as $index => $filter) {
            if ($filter['callback'] === $callback) {
                unset(self::$filters[$tag][$priority][$index]);
                
                // Clean up empty priority levels
                if (empty(self::$filters[$tag][$priority])) {
                    unset(self::$filters[$tag][$priority]);
                }
                
                // Clean up empty tags
                if (empty(self::$filters[$tag])) {
                    unset(self::$filters[$tag]);
                }
                
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Apply filter hooks
     * 
     * @param string $tag
     * @param mixed $value
     * @param mixed ...$args
     * @return mixed
     */
    public function applyFilters(string $tag, $value, ...$args): mixed
    {
        if (!isset(self::$filters[$tag])) {
            return $value;
        }
        
        $previousFilter = self::$currentFilter;
        self::$currentFilter = $tag;
        
        // Prepend the value to arguments
        array_unshift($args, $value);
        
        foreach (self::$filters[$tag] as $priority => $filters) {
            foreach ($filters as $filter) {
                $callback = $filter['callback'];
                $acceptedArgs = $filter['accepted_args'];
                
                // Limit arguments to accepted count
                $callArgs = array_slice($args, 0, $acceptedArgs);
                
                $value = call_user_func_array($callback, $callArgs);
                
                // Update the first argument with the new value
                $args[0] = $value;
            }
        }
        
        self::$currentFilter = $previousFilter;
        
        return $value;
    }
    
    /**
     * Check if action has hooks
     * 
     * @param string $tag
     * @return bool
     */
    public function hasAction(string $tag): bool
    {
        return isset(self::$actions[$tag]) && !empty(self::$actions[$tag]);
    }
    
    /**
     * Check if filter has hooks
     * 
     * @param string $tag
     * @return bool
     */
    public function hasFilter(string $tag): bool
    {
        return isset(self::$filters[$tag]) && !empty(self::$filters[$tag]);
    }
    
    /**
     * Get current filter being executed
     * 
     * @return string|null
     */
    public function getCurrentFilter(): ?string
    {
        return self::$currentFilter;
    }
    
    /**
     * Remove all hooks for a tag
     * 
     * @param string $tag
     * @param string $type 'action' or 'filter'
     */
    public function removeAllHooks(string $tag, string $type = 'both'): void
    {
        if ($type === 'action' || $type === 'both') {
            unset(self::$actions[$tag]);
        }
        
        if ($type === 'filter' || $type === 'both') {
            unset(self::$filters[$tag]);
        }
    }
    
    /**
     * Get all registered actions
     * 
     * @return array
     */
    public function getActions(): array
    {
        return self::$actions;
    }
    
    /**
     * Get all registered filters
     * 
     * @return array
     */
    public function getFilters(): array
    {
        return self::$filters;
    }
    
    /**
     * Get hooks for a specific tag
     * 
     * @param string $tag
     * @param string $type 'action' or 'filter'
     * @return array
     */
    public function getHooks(string $tag, string $type = 'both'): array
    {
        $hooks = [];
        
        if ($type === 'action' || $type === 'both') {
            $hooks['actions'] = self::$actions[$tag] ?? [];
        }
        
        if ($type === 'filter' || $type === 'both') {
            $hooks['filters'] = self::$filters[$tag] ?? [];
        }
        
        return $hooks;
    }
}

// Global helper functions for easier use
if (!function_exists('add_action')) {
    function add_action(string $tag, callable $callback, int $priority = 10, int $acceptedArgs = 1): void {
        $hook = Application::getInstance()->getHook();
        $hook->addAction($tag, $callback, $priority, $acceptedArgs);
    }
}

if (!function_exists('remove_action')) {
    function remove_action(string $tag, callable $callback, int $priority = 10): bool {
        $hook = Application::getInstance()->getHook();
        return $hook->removeAction($tag, $callback, $priority);
    }
}

if (!function_exists('do_action')) {
    function do_action(string $tag, ...$args): void {
        $hook = Application::getInstance()->getHook();
        $hook->doAction($tag, ...$args);
    }
}

if (!function_exists('add_filter')) {
    function add_filter(string $tag, callable $callback, int $priority = 10, int $acceptedArgs = 1): void {
        $hook = Application::getInstance()->getHook();
        $hook->addFilter($tag, $callback, $priority, $acceptedArgs);
    }
}

if (!function_exists('remove_filter')) {
    function remove_filter(string $tag, callable $callback, int $priority = 10): bool {
        $hook = Application::getInstance()->getHook();
        return $hook->removeFilter($tag, $callback, $priority);
    }
}

if (!function_exists('apply_filters')) {
    function apply_filters(string $tag, $value, ...$args): mixed {
        $hook = Application::getInstance()->getHook();
        return $hook->applyFilters($tag, $value, ...$args);
    }
}
