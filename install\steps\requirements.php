<?php
$requirements = $installer->checkRequirements();
$allRequired = true;
$hasWarnings = false;

foreach ($requirements as $req) {
    if ($req['required'] && !$req['status']) {
        $allRequired = false;
    }
    if (!$req['required'] && !$req['status']) {
        $hasWarnings = true;
    }
}
?>

<div class="requirements-content">
    <h2>System Requirements Check</h2>
    
    <p>We're checking if your server meets the minimum requirements to run ProjectManager.</p>
    
    <?php if (!$allRequired): ?>
        <div class="alert alert-danger">
            <strong>⚠️ Requirements Not Met</strong><br>
            Your server doesn't meet all the minimum requirements. Please contact your hosting provider or system administrator to resolve the issues below.
        </div>
    <?php elseif ($hasWarnings): ?>
        <div class="alert alert-warning">
            <strong>✅ Requirements Met with Warnings</strong><br>
            Your server meets the minimum requirements, but some recommended features are missing. You can proceed with the installation, but some features may not work optimally.
        </div>
    <?php else: ?>
        <div class="alert alert-success">
            <strong>✅ All Requirements Met</strong><br>
            Great! Your server meets all requirements and recommendations. You can proceed with confidence.
        </div>
    <?php endif; ?>
    
    <table class="requirements-table">
        <thead>
            <tr>
                <th>Requirement</th>
                <th>Status</th>
                <th>Current</th>
                <th>Message</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($requirements as $req): ?>
                <tr>
                    <td>
                        <?php echo htmlspecialchars($req['name']); ?>
                        <?php if ($req['required']): ?>
                            <span style="color: #dc3545; font-weight: bold;">*</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($req['status']): ?>
                            <span class="status-ok">✓ Pass</span>
                        <?php elseif ($req['required']): ?>
                            <span class="status-error">✗ Fail</span>
                        <?php else: ?>
                            <span class="status-warning">⚠ Warning</span>
                        <?php endif; ?>
                    </td>
                    <td><?php echo htmlspecialchars($req['current']); ?></td>
                    <td><?php echo htmlspecialchars($req['message']); ?></td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
    
    <div style="margin-top: 20px; font-size: 0.9rem; color: #666;">
        <strong>*</strong> Required for installation
    </div>
    
    <?php if (!$allRequired): ?>
        <div class="troubleshooting" style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <h3>Troubleshooting Tips</h3>
            
            <div style="margin: 15px 0;">
                <h4>PHP Version Issues:</h4>
                <ul>
                    <li>Contact your hosting provider to upgrade to PHP 8.1 or higher</li>
                    <li>If using shared hosting, check if you can change PHP version in control panel</li>
                    <li>For VPS/dedicated servers, update PHP through your package manager</li>
                </ul>
            </div>
            
            <div style="margin: 15px 0;">
                <h4>Missing PHP Extensions:</h4>
                <ul>
                    <li>Contact your hosting provider to enable missing extensions</li>
                    <li>For VPS/dedicated servers, install extensions using: <code>apt-get install php-[extension]</code> or <code>yum install php-[extension]</code></li>
                    <li>Restart your web server after installing extensions</li>
                </ul>
            </div>
            
            <div style="margin: 15px 0;">
                <h4>File Permission Issues:</h4>
                <ul>
                    <li>Set directory permissions to 755: <code>chmod 755 directory_name</code></li>
                    <li>Set file permissions to 644: <code>chmod 644 file_name</code></li>
                    <li>Ensure your web server user has write access to the directories</li>
                    <li>For shared hosting, use your file manager or FTP client to change permissions</li>
                </ul>
            </div>
            
            <div style="margin: 15px 0;">
                <h4>Memory Limit Issues:</h4>
                <ul>
                    <li>Add <code>memory_limit = 128M</code> to your php.ini file</li>
                    <li>For shared hosting, try adding <code>ini_set('memory_limit', '128M');</code> to a .htaccess file</li>
                    <li>Contact your hosting provider if you cannot change memory limits</li>
                </ul>
            </div>
        </div>
    <?php endif; ?>
    
    <div class="next-steps" style="margin-top: 30px; padding: 20px; background: #e3f2fd; border-radius: 8px;">
        <h3>Next Steps</h3>
        <?php if ($allRequired): ?>
            <p>✅ Your server is ready for ProjectManager installation. Click "Continue" to proceed to database configuration.</p>
        <?php else: ?>
            <p>❌ Please resolve the failed requirements above before continuing. You can refresh this page after making changes to re-check the requirements.</p>
        <?php endif; ?>
    </div>
</div>

<form id="install-form" method="post" style="display: none;">
    <!-- Hidden form for navigation -->
</form>

<?php if (!$allRequired): ?>
<script>
// Disable continue button if requirements not met
document.addEventListener('DOMContentLoaded', function() {
    const continueBtn = document.querySelector('button[type="submit"]');
    if (continueBtn) {
        continueBtn.disabled = true;
        continueBtn.style.opacity = '0.5';
        continueBtn.style.cursor = 'not-allowed';
        continueBtn.innerHTML = 'Requirements Not Met';
    }
});
</script>
<?php endif; ?>
