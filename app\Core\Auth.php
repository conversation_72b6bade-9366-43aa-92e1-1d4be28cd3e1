<?php
/**
 * Authentication Class
 * 
 * Handle user authentication and session management
 * 
 * @package ProjectManager\Core
 */

declare(strict_types=1);

namespace App\Core;

use Exception;

/**
 * Authentication Class
 */
class Auth
{
    /**
     * Database instance
     * 
     * @var Database
     */
    private Database $database;
    
    /**
     * Current user data
     * 
     * @var array|null
     */
    private ?array $currentUser = null;
    
    /**
     * Session started flag
     * 
     * @var bool
     */
    private bool $sessionStarted = false;
    
    /**
     * Auth constructor
     * 
     * @param Database $database
     */
    public function __construct(Database $database)
    {
        $this->database = $database;
    }
    
    /**
     * Start session with secure settings
     */
    public function startSession(): void
    {
        if ($this->sessionStarted || session_status() === PHP_SESSION_ACTIVE) {
            return;
        }
        
        // Configure session settings
        ini_set('session.cookie_httponly', '1');
        ini_set('session.use_only_cookies', '1');
        ini_set('session.cookie_secure', $this->isHttps() ? '1' : '0');
        ini_set('session.cookie_samesite', 'Strict');
        
        if (defined('PM_SESSION_LIFETIME')) {
            ini_set('session.gc_maxlifetime', (string) PM_SESSION_LIFETIME);
            ini_set('session.cookie_lifetime', (string) PM_SESSION_LIFETIME);
        }
        
        session_start();
        $this->sessionStarted = true;
        
        // Regenerate session ID periodically for security
        if (!isset($_SESSION['last_regeneration'])) {
            $this->regenerateSession();
        } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
            $this->regenerateSession();
        }
        
        // Load current user if logged in
        if (isset($_SESSION['user_id'])) {
            $this->loadCurrentUser((int) $_SESSION['user_id']);
        }
    }
    
    /**
     * Regenerate session ID
     */
    private function regenerateSession(): void
    {
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }
    
    /**
     * Check if connection is HTTPS
     * 
     * @return bool
     */
    private function isHttps(): bool
    {
        return isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
    }
    
    /**
     * Attempt to log in a user
     * 
     * @param string $email
     * @param string $password
     * @return bool
     */
    public function login(string $email, string $password): bool
    {
        // Check rate limiting
        if (!$this->checkRateLimit($email)) {
            return false;
        }
        
        // Find user by email
        $user = $this->database->queryOne(
            "SELECT * FROM {$this->database->getTable('users')} WHERE email = ? AND status = 'active' AND deleted_at IS NULL",
            [$email]
        );
        
        if (!$user) {
            $this->recordFailedAttempt($email);
            return false;
        }
        
        // Verify password
        if (!password_verify($password, $user['password'])) {
            $this->recordFailedAttempt($email);
            return false;
        }
        
        // Clear failed attempts
        $this->clearFailedAttempts($email);
        
        // Update last login
        $this->database->update('users', [
            'last_login_at' => date('Y-m-d H:i:s')
        ], ['id' => $user['id']]);
        
        // Set session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_email'] = $user['email'];
        
        $this->currentUser = $user;
        
        return true;
    }
    
    /**
     * Log out the current user
     */
    public function logout(): void
    {
        if ($this->sessionStarted) {
            session_destroy();
        }
        
        $this->currentUser = null;
        $this->sessionStarted = false;
    }
    
    /**
     * Check if user is logged in
     * 
     * @return bool
     */
    public function isLoggedIn(): bool
    {
        return $this->currentUser !== null;
    }
    
    /**
     * Get current user
     * 
     * @return array|null
     */
    public function getCurrentUser(): ?array
    {
        return $this->currentUser;
    }
    
    /**
     * Get current user ID
     * 
     * @return int|null
     */
    public function getCurrentUserId(): ?int
    {
        return $this->currentUser ? (int) $this->currentUser['id'] : null;
    }
    
    /**
     * Load current user from database
     * 
     * @param int $userId
     */
    private function loadCurrentUser(int $userId): void
    {
        $user = $this->database->queryOne(
            "SELECT * FROM {$this->database->getTable('users')} WHERE id = ? AND status = 'active' AND deleted_at IS NULL",
            [$userId]
        );
        
        if ($user) {
            $this->currentUser = $user;
        } else {
            // User not found or inactive, clear session
            $this->logout();
        }
    }
    
    /**
     * Hash a password
     * 
     * @param string $password
     * @return string
     */
    public function hashPassword(string $password): string
    {
        return password_hash($password, PASSWORD_ARGON2ID);
    }
    
    /**
     * Generate CSRF token
     * 
     * @return string
     */
    public function generateCsrfToken(): string
    {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Verify CSRF token
     * 
     * @param string $token
     * @return bool
     */
    public function verifyCsrfToken(string $token): bool
    {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Check rate limiting for login attempts
     * 
     * @param string $email
     * @return bool
     */
    private function checkRateLimit(string $email): bool
    {
        $attempts = $this->getFailedAttempts($email);
        $maxAttempts = defined('PM_LOGIN_ATTEMPTS') ? PM_LOGIN_ATTEMPTS : 5;
        
        return $attempts < $maxAttempts;
    }
    
    /**
     * Record failed login attempt
     * 
     * @param string $email
     */
    private function recordFailedAttempt(string $email): void
    {
        $key = 'login_attempts_' . md5($email);
        $attempts = (int) ($_SESSION[$key] ?? 0);
        $_SESSION[$key] = $attempts + 1;
        $_SESSION[$key . '_time'] = time();
    }
    
    /**
     * Get failed login attempts
     * 
     * @param string $email
     * @return int
     */
    private function getFailedAttempts(string $email): int
    {
        $key = 'login_attempts_' . md5($email);
        $lockoutTime = defined('PM_LOGIN_LOCKOUT_TIME') ? PM_LOGIN_LOCKOUT_TIME : 900;
        
        if (isset($_SESSION[$key . '_time']) && (time() - $_SESSION[$key . '_time']) > $lockoutTime) {
            // Reset attempts after lockout period
            unset($_SESSION[$key], $_SESSION[$key . '_time']);
            return 0;
        }
        
        return (int) ($_SESSION[$key] ?? 0);
    }
    
    /**
     * Clear failed login attempts
     * 
     * @param string $email
     */
    private function clearFailedAttempts(string $email): void
    {
        $key = 'login_attempts_' . md5($email);
        unset($_SESSION[$key], $_SESSION[$key . '_time']);
    }
    
    /**
     * Check if user has permission
     * 
     * @param string $permission
     * @return bool
     */
    public function hasPermission(string $permission): bool
    {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        // TODO: Implement role-based permissions
        // For now, all logged-in users have basic permissions
        return true;
    }
    
    /**
     * Require authentication
     * 
     * @throws Exception
     */
    public function requireAuth(): void
    {
        if (!$this->isLoggedIn()) {
            throw new Exception('Authentication required');
        }
    }
    
    /**
     * Require permission
     * 
     * @param string $permission
     * @throws Exception
     */
    public function requirePermission(string $permission): void
    {
        $this->requireAuth();
        
        if (!$this->hasPermission($permission)) {
            throw new Exception('Insufficient permissions');
        }
    }
}
