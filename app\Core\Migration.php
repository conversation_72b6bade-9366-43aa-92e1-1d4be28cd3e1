<?php
/**
 * Database Migration System
 * 
 * Handle database schema migrations
 * 
 * @package ProjectManager\Core
 */

declare(strict_types=1);

namespace App\Core;

use Exception;

/**
 * Migration Class
 */
class Migration
{
    /**
     * Database instance
     * 
     * @var Database
     */
    private Database $database;
    
    /**
     * Migration directory
     * 
     * @var string
     */
    private string $migrationDir;
    
    /**
     * Migration constructor
     * 
     * @param Database $database
     */
    public function __construct(Database $database)
    {
        $this->database = $database;
        $this->migrationDir = PM_ROOT . '/updates/migrations';
    }
    
    /**
     * Run pending migrations
     * 
     * @return array
     */
    public function runPending(): array
    {
        $executed = [];
        $pendingMigrations = $this->getPendingMigrations();
        
        if (empty($pendingMigrations)) {
            return $executed;
        }
        
        $batch = $this->getNextBatchNumber();
        
        foreach ($pendingMigrations as $migration) {
            try {
                $this->database->beginTransaction();
                
                $this->executeMigration($migration);
                $this->recordMigration($migration, $batch);
                
                $this->database->commit();
                $executed[] = $migration;
                
            } catch (Exception $e) {
                $this->database->rollback();
                throw new Exception("Migration failed: {$migration} - " . $e->getMessage());
            }
        }
        
        return $executed;
    }
    
    /**
     * Get pending migrations
     * 
     * @return array
     */
    public function getPendingMigrations(): array
    {
        $allMigrations = $this->getAllMigrationFiles();
        $executedMigrations = $this->getExecutedMigrations();
        
        return array_diff($allMigrations, $executedMigrations);
    }
    
    /**
     * Get all migration files
     * 
     * @return array
     */
    private function getAllMigrationFiles(): array
    {
        if (!is_dir($this->migrationDir)) {
            return [];
        }
        
        $files = scandir($this->migrationDir);
        $migrations = [];
        
        foreach ($files as $file) {
            if (preg_match('/^\d{3}_.*\.sql$/', $file)) {
                $migrations[] = pathinfo($file, PATHINFO_FILENAME);
            }
        }
        
        sort($migrations);
        return $migrations;
    }
    
    /**
     * Get executed migrations
     * 
     * @return array
     */
    private function getExecutedMigrations(): array
    {
        // Check if migrations table exists
        if (!$this->database->tableExists('migrations')) {
            return [];
        }
        
        $result = $this->database->query(
            "SELECT migration FROM {$this->database->getTable('migrations')} ORDER BY id"
        );
        
        return array_column($result, 'migration');
    }
    
    /**
     * Execute a migration
     * 
     * @param string $migration
     * @throws Exception
     */
    private function executeMigration(string $migration): void
    {
        $migrationFile = $this->migrationDir . '/' . $migration . '.sql';
        
        if (!file_exists($migrationFile)) {
            throw new Exception("Migration file not found: {$migrationFile}");
        }
        
        $sql = file_get_contents($migrationFile);
        if ($sql === false) {
            throw new Exception("Could not read migration file: {$migrationFile}");
        }
        
        // Replace table prefix placeholder
        $tablePrefix = $GLOBALS['table_prefix'] ?? 'pm_';
        $sql = str_replace('{PREFIX}', $tablePrefix, $sql);
        
        // Split SQL into individual statements
        $statements = $this->splitSqlStatements($sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $this->database->prepare($statement);
            }
        }
    }
    
    /**
     * Split SQL into individual statements
     * 
     * @param string $sql
     * @return array
     */
    private function splitSqlStatements(string $sql): array
    {
        // Remove comments
        $sql = preg_replace('/--.*$/m', '', $sql);
        $sql = preg_replace('/\/\*.*?\*\//s', '', $sql);
        
        // Split by semicolon, but not within quotes
        $statements = [];
        $current = '';
        $inQuotes = false;
        $quoteChar = '';
        
        for ($i = 0; $i < strlen($sql); $i++) {
            $char = $sql[$i];
            
            if (!$inQuotes && ($char === '"' || $char === "'")) {
                $inQuotes = true;
                $quoteChar = $char;
            } elseif ($inQuotes && $char === $quoteChar) {
                // Check for escaped quote
                if ($i > 0 && $sql[$i - 1] !== '\\') {
                    $inQuotes = false;
                    $quoteChar = '';
                }
            } elseif (!$inQuotes && $char === ';') {
                $statements[] = $current;
                $current = '';
                continue;
            }
            
            $current .= $char;
        }
        
        if (!empty(trim($current))) {
            $statements[] = $current;
        }
        
        return $statements;
    }
    
    /**
     * Record migration execution
     * 
     * @param string $migration
     * @param int $batch
     */
    private function recordMigration(string $migration, int $batch): void
    {
        $this->database->insert('migrations', [
            'migration' => $migration,
            'batch' => $batch
        ]);
    }
    
    /**
     * Get next batch number
     * 
     * @return int
     */
    private function getNextBatchNumber(): int
    {
        if (!$this->database->tableExists('migrations')) {
            return 1;
        }
        
        $result = $this->database->queryOne(
            "SELECT MAX(batch) as max_batch FROM {$this->database->getTable('migrations')}"
        );
        
        return ($result['max_batch'] ?? 0) + 1;
    }
    
    /**
     * Rollback last batch of migrations
     * 
     * @return array
     */
    public function rollbackLastBatch(): array
    {
        $lastBatch = $this->getLastBatch();
        
        if (empty($lastBatch)) {
            return [];
        }
        
        $rolledBack = [];
        
        // Reverse order for rollback
        $migrations = array_reverse($lastBatch);
        
        foreach ($migrations as $migration) {
            try {
                $this->database->beginTransaction();
                
                $this->rollbackMigration($migration['migration']);
                $this->removeMigrationRecord($migration['id']);
                
                $this->database->commit();
                $rolledBack[] = $migration['migration'];
                
            } catch (Exception $e) {
                $this->database->rollback();
                throw new Exception("Rollback failed: {$migration['migration']} - " . $e->getMessage());
            }
        }
        
        return $rolledBack;
    }
    
    /**
     * Get last batch of migrations
     * 
     * @return array
     */
    private function getLastBatch(): array
    {
        if (!$this->database->tableExists('migrations')) {
            return [];
        }
        
        $lastBatchNumber = $this->database->queryOne(
            "SELECT MAX(batch) as max_batch FROM {$this->database->getTable('migrations')}"
        );
        
        if (!$lastBatchNumber || !$lastBatchNumber['max_batch']) {
            return [];
        }
        
        return $this->database->query(
            "SELECT * FROM {$this->database->getTable('migrations')} WHERE batch = ? ORDER BY id",
            [$lastBatchNumber['max_batch']]
        );
    }
    
    /**
     * Rollback a migration
     * 
     * @param string $migration
     * @throws Exception
     */
    private function rollbackMigration(string $migration): void
    {
        $rollbackFile = $this->migrationDir . '/rollback/' . $migration . '.sql';
        
        if (!file_exists($rollbackFile)) {
            throw new Exception("Rollback file not found: {$rollbackFile}");
        }
        
        $sql = file_get_contents($rollbackFile);
        if ($sql === false) {
            throw new Exception("Could not read rollback file: {$rollbackFile}");
        }
        
        // Replace table prefix placeholder
        $tablePrefix = $GLOBALS['table_prefix'] ?? 'pm_';
        $sql = str_replace('{PREFIX}', $tablePrefix, $sql);
        
        // Split SQL into individual statements
        $statements = $this->splitSqlStatements($sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $this->database->prepare($statement);
            }
        }
    }
    
    /**
     * Remove migration record
     * 
     * @param int $id
     */
    private function removeMigrationRecord(int $id): void
    {
        $this->database->delete('migrations', ['id' => $id]);
    }
    
    /**
     * Get migration status
     * 
     * @return array
     */
    public function getStatus(): array
    {
        $allMigrations = $this->getAllMigrationFiles();
        $executedMigrations = $this->getExecutedMigrations();
        $pendingMigrations = array_diff($allMigrations, $executedMigrations);
        
        return [
            'total' => count($allMigrations),
            'executed' => count($executedMigrations),
            'pending' => count($pendingMigrations),
            'executed_list' => $executedMigrations,
            'pending_list' => $pendingMigrations
        ];
    }
}
