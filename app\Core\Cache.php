<?php
/**
 * File-based Cache Class
 * 
 * Simple file-based caching system for shared hosting
 * 
 * @package ProjectManager\Core
 */

declare(strict_types=1);

namespace App\Core;

use Exception;

/**
 * Cache Class
 */
class Cache
{
    /**
     * Cache directory
     * 
     * @var string
     */
    private string $cacheDir;
    
    /**
     * Default cache lifetime in seconds
     * 
     * @var int
     */
    private int $defaultLifetime;
    
    /**
     * Cache enabled flag
     * 
     * @var bool
     */
    private bool $enabled;
    
    /**
     * Cache constructor
     */
    public function __construct()
    {
        $this->cacheDir = PM_STORAGE_PATH . '/cache';
        $this->defaultLifetime = defined('PM_CACHE_LIFETIME') ? PM_CACHE_LIFETIME : 3600;
        $this->enabled = defined('PM_CACHE_ENABLED') ? PM_CACHE_ENABLED : true;
        
        $this->ensureCacheDirectory();
    }
    
    /**
     * Ensure cache directory exists
     */
    private function ensureCacheDirectory(): void
    {
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
        
        // Create .htaccess to protect cache directory
        $htaccessFile = $this->cacheDir . '/.htaccess';
        if (!file_exists($htaccessFile)) {
            file_put_contents($htaccessFile, "Order deny,allow\nDeny from all\n");
        }
    }
    
    /**
     * Get cache file path
     * 
     * @param string $key
     * @return string
     */
    private function getCacheFilePath(string $key): string
    {
        $hash = md5($key);
        $subDir = substr($hash, 0, 2);
        $dir = $this->cacheDir . '/' . $subDir;
        
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        return $dir . '/' . $hash . '.cache';
    }
    
    /**
     * Store data in cache
     * 
     * @param string $key
     * @param mixed $data
     * @param int|null $lifetime
     * @return bool
     */
    public function set(string $key, $data, ?int $lifetime = null): bool
    {
        if (!$this->enabled) {
            return false;
        }
        
        $lifetime = $lifetime ?? $this->defaultLifetime;
        $expiry = time() + $lifetime;
        
        $cacheData = [
            'expiry' => $expiry,
            'data' => $data
        ];
        
        $filePath = $this->getCacheFilePath($key);
        
        try {
            $serialized = serialize($cacheData);
            return file_put_contents($filePath, $serialized, LOCK_EX) !== false;
        } catch (Exception $e) {
            error_log('Cache write error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Retrieve data from cache
     * 
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function get(string $key, $default = null)
    {
        if (!$this->enabled) {
            return $default;
        }
        
        $filePath = $this->getCacheFilePath($key);
        
        if (!file_exists($filePath)) {
            return $default;
        }
        
        try {
            $content = file_get_contents($filePath);
            if ($content === false) {
                return $default;
            }
            
            $cacheData = unserialize($content);
            
            if (!is_array($cacheData) || !isset($cacheData['expiry'], $cacheData['data'])) {
                $this->delete($key);
                return $default;
            }
            
            // Check if cache has expired
            if (time() > $cacheData['expiry']) {
                $this->delete($key);
                return $default;
            }
            
            return $cacheData['data'];
            
        } catch (Exception $e) {
            error_log('Cache read error: ' . $e->getMessage());
            $this->delete($key);
            return $default;
        }
    }
    
    /**
     * Check if cache key exists and is valid
     * 
     * @param string $key
     * @return bool
     */
    public function has(string $key): bool
    {
        if (!$this->enabled) {
            return false;
        }
        
        $filePath = $this->getCacheFilePath($key);
        
        if (!file_exists($filePath)) {
            return false;
        }
        
        try {
            $content = file_get_contents($filePath);
            if ($content === false) {
                return false;
            }
            
            $cacheData = unserialize($content);
            
            if (!is_array($cacheData) || !isset($cacheData['expiry'])) {
                $this->delete($key);
                return false;
            }
            
            // Check if cache has expired
            if (time() > $cacheData['expiry']) {
                $this->delete($key);
                return false;
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log('Cache check error: ' . $e->getMessage());
            $this->delete($key);
            return false;
        }
    }
    
    /**
     * Delete cache entry
     * 
     * @param string $key
     * @return bool
     */
    public function delete(string $key): bool
    {
        $filePath = $this->getCacheFilePath($key);
        
        if (file_exists($filePath)) {
            return unlink($filePath);
        }
        
        return true;
    }
    
    /**
     * Clear all cache
     * 
     * @return bool
     */
    public function clear(): bool
    {
        try {
            $this->deleteDirectory($this->cacheDir);
            $this->ensureCacheDirectory();
            return true;
        } catch (Exception $e) {
            error_log('Cache clear error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get or set cache with callback
     * 
     * @param string $key
     * @param callable $callback
     * @param int|null $lifetime
     * @return mixed
     */
    public function remember(string $key, callable $callback, ?int $lifetime = null)
    {
        $value = $this->get($key);
        
        if ($value !== null) {
            return $value;
        }
        
        $value = $callback();
        $this->set($key, $value, $lifetime);
        
        return $value;
    }
    
    /**
     * Increment cache value
     * 
     * @param string $key
     * @param int $value
     * @return int|false
     */
    public function increment(string $key, int $value = 1)
    {
        $current = $this->get($key, 0);
        
        if (!is_numeric($current)) {
            return false;
        }
        
        $new = (int) $current + $value;
        $this->set($key, $new);
        
        return $new;
    }
    
    /**
     * Decrement cache value
     * 
     * @param string $key
     * @param int $value
     * @return int|false
     */
    public function decrement(string $key, int $value = 1)
    {
        return $this->increment($key, -$value);
    }
    
    /**
     * Clean expired cache entries
     * 
     * @return int Number of cleaned entries
     */
    public function cleanExpired(): int
    {
        $cleaned = 0;
        
        try {
            $iterator = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($this->cacheDir, \RecursiveDirectoryIterator::SKIP_DOTS)
            );
            
            foreach ($iterator as $file) {
                if ($file->getExtension() === 'cache') {
                    $content = file_get_contents($file->getPathname());
                    if ($content !== false) {
                        $cacheData = unserialize($content);
                        
                        if (is_array($cacheData) && isset($cacheData['expiry']) && time() > $cacheData['expiry']) {
                            unlink($file->getPathname());
                            $cleaned++;
                        }
                    }
                }
            }
        } catch (Exception $e) {
            error_log('Cache cleanup error: ' . $e->getMessage());
        }
        
        return $cleaned;
    }
    
    /**
     * Get cache statistics
     * 
     * @return array
     */
    public function getStats(): array
    {
        $stats = [
            'enabled' => $this->enabled,
            'directory' => $this->cacheDir,
            'total_files' => 0,
            'total_size' => 0,
            'expired_files' => 0
        ];
        
        if (!is_dir($this->cacheDir)) {
            return $stats;
        }
        
        try {
            $iterator = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($this->cacheDir, \RecursiveDirectoryIterator::SKIP_DOTS)
            );
            
            foreach ($iterator as $file) {
                if ($file->getExtension() === 'cache') {
                    $stats['total_files']++;
                    $stats['total_size'] += $file->getSize();
                    
                    $content = file_get_contents($file->getPathname());
                    if ($content !== false) {
                        $cacheData = unserialize($content);
                        
                        if (is_array($cacheData) && isset($cacheData['expiry']) && time() > $cacheData['expiry']) {
                            $stats['expired_files']++;
                        }
                    }
                }
            }
        } catch (Exception $e) {
            error_log('Cache stats error: ' . $e->getMessage());
        }
        
        return $stats;
    }
    
    /**
     * Recursively delete directory
     * 
     * @param string $dir
     */
    private function deleteDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = array_diff(scandir($dir), ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            
            if (is_dir($path)) {
                $this->deleteDirectory($path);
            } else {
                unlink($path);
            }
        }
        
        rmdir($dir);
    }
}
